using System.Collections.Generic;
using UnityEngine;

namespace Game.Hotfix
{
    public class BattleBulletLightningBoltLink : BattleBullet
    {  
        private SkillTargetData m_SkillTargetData;
        private Vector3 m_MoveBeginPos;
        private Vector3 m_TargetPos;

        private EditorBulletLightningBoltLink m_Param;
        
        private BattleEffect m_BeginEffect;
        private List<BattleEffect> m_EndEffects;

        private List<Transform> m_LightningList;
        private List<LightningBoltScript[]> m_LightningBoltScripts;
        
        protected override void OnInit()
        {
            if (!string.IsNullOrEmpty(SkillShowCfg.b_trajectory_param))
                m_Param = JsonUtility.FromJson<EditorBulletLightningBoltLink>(SkillShowCfg.b_trajectory_param);
            
            m_SkillTargetData = GetFirstTarget();
            m_MoveBeginPos = GetPosition();
            m_TargetPos = m_SkillTargetData.TargetPos + GetRandomBombRange();
            
            int beginEffect = GetEffectBegin();
            if (beginEffect > 0)
            {
                m_BeginEffect = m_BattleFiled.EffectCtrl.GetEffect(beginEffect);
                m_BeginEffect.SetPosition(m_MoveBeginPos);
            }

            m_EndEffects = new List<BattleEffect>();
        }

        protected override void OnBulletTick(float dt)
        {
            if (AlivePct >= 1)
                m_BattleFiled.BulletCtrl.RemoveBullet(this, true);
        }

        protected override void OnUnInit()
        {
            if (m_BeginEffect != null)
            {
                m_BattleFiled.EffectCtrl.RemoveEffect(m_BeginEffect);
                m_BeginEffect = null;
            }

            foreach (var effect in m_EndEffects)
            {
                m_BattleFiled.EffectCtrl.RemoveEffect(effect);
            }

            m_EndEffects.Clear();
        }

        protected override void OnLoaded(Entity entity)
        {
            base.OnLoaded(entity);

            m_LightningList = new List<Transform>();
            m_LightningBoltScripts = new List<LightningBoltScript[]>();
            for (int i = 0; i < m_Param.LightningList.Count; i++)
            {
                var trans = entity.transform.Find(m_Param.LightningList[i]);
                if (trans != null)
                {
                    m_LightningList.Add(trans);
                    var ls = trans.GetComponentsInChildren<LightningBoltScript>();
                    m_LightningBoltScripts.Add(ls);
                }
            }

            Vector3? posBegin = null;
            for (int i = 0; i < m_LightningList.Count; i++)
            {
                if (i < m_TargetList.Count)
                {
                    if (posBegin == null)
                        posBegin = m_MoveBeginPos;
                    Vector3 targetPos = m_TargetList[i].TargetPos;
                    
                    m_LightningList[i].gameObject.SetActive(true);
                    foreach (var script in m_LightningBoltScripts[i])
                    {
                        script.StartPosition = posBegin.Value;
                        script.EndPosition = targetPos;
                    }

                    var endEffectId = GetEffectEnd();
                    if (endEffectId > 0)
                    {
                        var endEffect = m_BattleFiled.EffectCtrl.GetEffect(endEffectId);
                        endEffect.SetPosition(targetPos);
                        m_EndEffects.Add(endEffect);
                    }
                    

                    posBegin = targetPos;
                }
                else
                {
                    m_LightningList[i].gameObject.SetActive(false);
                }
            }
        }

        private int GetEffectBegin()
        {
            if (m_Param != null)
            {
                return m_Param.BeginEffectId;
            }

            return 0;
        }

        private int GetEffectEnd()
        {
            if (m_Param != null)
            {
                return m_Param.EndEffectId;
            }

            return 0;
        }
    }
}
