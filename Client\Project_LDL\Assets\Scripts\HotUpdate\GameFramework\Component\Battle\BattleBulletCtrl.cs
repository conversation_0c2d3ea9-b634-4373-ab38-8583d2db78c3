using System;
using System.Collections.Generic;
using Game.Hotfix.Config;
using GameFramework;
using UnityEngine;

namespace Game.Hotfix
{
    public class BattleBulletCtrl:BattleBaseCtrl
    {
        private List<BattleBullet> m_BulletList;

        public override void Init(BattleFiled battleFiled)
        {
            base.Init(battleFiled);
            m_BulletList = new List<BattleBullet>();
        }

        public override void RemoveAll()
        {
            for (int i = 0; i < m_BulletList.Count; i++)
            {
                RemoveBullet(m_BulletList[i], false);
            }
            m_BulletList.Clear();
        }

        public override void UnInit()
        {
            RemoveAll();
        }

        public override void OnTick(float dt)
        {
            for (var i = 0; i < m_BulletList.Count; i++)
            {
                if (!m_BulletList[i].IsDie)
                    m_BulletList[i].Tick(dt);
            }

            for (var i = m_BulletList.Count-1; i >=0; i--)
            {
                if (m_BulletList[i].IsDie)
                {
                    m_BulletList.RemoveAt(i);
                }
            }
        }

        public void RemoveBullet(BattleBullet bullet,bool playBombEffect)
        {
            if (playBombEffect)
                bullet.PlayBombEffectAuto();
            ReferencePool.Release(bullet);
            m_BulletList.Remove(bullet);
        } 
        
        public BattleBullet CreateBullet(skill_show config,List<SkillTargetData> targetList,Vector3 position,Quaternion rotation,bool isFake = false)
        {
            BattleBullet bullet = null;

            switch (config.b_trajectory)
            {
                case trajectory.bullet_normal:
                    bullet = ReferencePool.Acquire<BattleBulletNormal>();
                    break;
                case trajectory.bullet_parabolic:
                    bullet = ReferencePool.Acquire<BattleBulletParabolic>();
                    break;
                case trajectory.bullet_laser:
                    bullet = ReferencePool.Acquire<BattleBulletLaser>();
                    break;
                case trajectory.bullet_bezier:
                    bullet = ReferencePool.Acquire<BattleBulletBezier>();
                    break;
                case trajectory.bullet_particle:
                    bullet = ReferencePool.Acquire<BattleBulletParticle>();
                    break;
                case trajectory.bullet_lightningbolt:
                    bullet = ReferencePool.Acquire<BattleBulletLightningBolt>();
                    break;
                case trajectory.bullet_lightningboltlink:
                    bullet = ReferencePool.Acquire<BattleBulletLightningBoltLink>();
                    break;
                default:
                    bullet = ReferencePool.Acquire<BattleBulletEmpty>();
                    break;
            }
            bullet.SetPosition(position);
            bullet.SetRotation(rotation);
            bullet.SetScale(Vector3.one);
            bullet.Init(m_BattleFiled,config,targetList,isFake);

            m_BulletList.Add(bullet);
            
            return bullet;
        }
        
    }
}
