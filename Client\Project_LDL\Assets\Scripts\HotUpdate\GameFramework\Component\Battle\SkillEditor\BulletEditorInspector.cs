using System;
using System.Collections.Generic;
using Sirenix.OdinInspector;
using UnityEngine;

namespace Game.Hotfix
{
    [Serializable]
    public class BulletEditorInspectorBase
    {
    }

    [Serializable]
    public class EditorBulletNone : BulletEditorInspectorBase
    {
    }

    [Serializable]
    public class EditorBulletNormal : BulletEditorInspectorBase
    {
        [LabelText("表演子弹数量")] public float Count = 0;
        [LabelText("扇形角度")] public float Angle = 0;
        [LabelText("是否随机")] public bool Random = false;
    }

    [Serializable]
    public class EditorBulletParabolic : BulletEditorInspectorBase
    {
        [LabelText("抛物线高度")] public float Height;
    }

    [Serializable]
    public class EditorBulletLaser : BulletEditorInspectorBase
    {
        [LabelText("激光起点特效")] public int BeginEffectId = 0;
        [LabelText("激光终点特效")] public int EndEffectId = 0;
    }

    [Serializable]
    public class EditorBulletBezier : BulletEditorInspectorBase
    {
        [LabelText("旋转类型")] public BulletBezierRotationType rotateType;
        [LabelText("曲线两端抓手A")] public Vector2 Point1;
        [LabelText("曲线两端抓手B")] public Vector2 Point2;
        [LabelText("抓手A随机半径")] public float RandomRangeA = 0;
        [LabelText("抓手B随机半径")] public float RandomRangeB = 0;
    }

    [Serializable]
    public class EditorBulletParticle : BulletEditorInspectorBase
    {
        [LabelText("喷射区间")] public float Offset = 0;
        [LabelText("喷射最小速度")] public float MinSpeed = 0;
        [LabelText("喷射最大速度")] public float MaxSpeed = 0;
        [LabelText("喷射最小射程")] public float MinDistance = 0;
        [LabelText("喷射最大射程")] public float MaxDistance = 0;
    }

    [Serializable]
    public class EditorBulletLaserLink : BulletEditorInspectorBase
    {
        // public AnimationCurve Curve;
        //
        // [Button("测试")]
        // private void Test()
        // {
        //     Curve = AnimationCurve.EaseInOut(0, 0, 1, 1);
        // }
    }

    [Serializable]
    public class EditorBulletLightningBolt : BulletEditorInspectorBase
    {
        [LabelText("闪电起点特效")] public int BeginEffectId = 0;
        [LabelText("闪电终点特效")] public int EndEffectId = 0;
    }

    [Serializable]
    public class EditorBulletBouncing : BulletEditorInspectorBase
    {
        [LabelText("高度")] public float Height = 0;
        [LabelText("次数")] public float Count = 0;
    }
    
    [Serializable]
    public class EditorBulletLightningBoltLink : BulletEditorInspectorBase
    {
        [LabelText("闪电起点特效")] public int BeginEffectId = 0;
        [LabelText("闪电终点特效")] public int EndEffectId = 0;
        [LabelText("闪电特效组")] public List<string> LightningList = new List<string>();

    }
}