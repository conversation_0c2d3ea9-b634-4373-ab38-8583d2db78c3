-target:library
-out:"Library/Bee/artifacts/1300b0aEDbg.dag/Game.Hotfix.dll"
-refout:"Library/Bee/artifacts/1300b0aEDbg.dag/Game.Hotfix.ref.dll"
-define:UNITY_6000_0_28
-define:UNITY_6000_0
-define:UNITY_6000
-define:UNITY_5_3_OR_NEWER
-define:UNITY_5_4_OR_NEWER
-define:UNITY_5_5_OR_NEWER
-define:UNITY_5_6_OR_NEWER
-define:UNITY_2017_1_OR_NEWER
-define:UNITY_2017_2_OR_NEWER
-define:UNITY_2017_3_OR_NEWER
-define:UNITY_2017_4_OR_NEWER
-define:UNITY_2018_1_OR_NEWER
-define:UNITY_2018_2_OR_NEWER
-define:UNITY_2018_3_OR_NEWER
-define:UNITY_2018_4_OR_NEWER
-define:UNITY_2019_1_OR_NEWER
-define:UNITY_2019_2_OR_NEWER
-define:UNITY_2019_3_OR_NEWER
-define:UNITY_2019_4_OR_NEWER
-define:UNITY_2020_1_OR_NEWER
-define:UNITY_2020_2_OR_NEWER
-define:UNITY_2020_3_OR_NEWER
-define:UNITY_2021_1_OR_NEWER
-define:UNITY_2021_2_OR_NEWER
-define:UNITY_2021_3_OR_NEWER
-define:UNITY_2022_1_OR_NEWER
-define:UNITY_2022_2_OR_NEWER
-define:UNITY_2022_3_OR_NEWER
-define:UNITY_2023_1_OR_NEWER
-define:UNITY_2023_2_OR_NEWER
-define:UNITY_2023_3_OR_NEWER
-define:UNITY_6000_0_OR_NEWER
-define:UNITY_INCLUDE_TESTS
-define:ENABLE_AR
-define:ENABLE_AUDIO
-define:ENABLE_CACHING
-define:ENABLE_CLOTH
-define:ENABLE_EVENT_QUEUE
-define:ENABLE_MICROPHONE
-define:ENABLE_MULTIPLE_DISPLAYS
-define:ENABLE_PHYSICS
-define:ENABLE_TEXTURE_STREAMING
-define:ENABLE_LZMA
-define:ENABLE_UNITYEVENTS
-define:ENABLE_VR
-define:ENABLE_WEBCAM
-define:ENABLE_UNITYWEBREQUEST
-define:ENABLE_WWW
-define:ENABLE_CLOUD_SERVICES
-define:ENABLE_CLOUD_SERVICES_ADS
-define:ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
-define:ENABLE_CLOUD_SERVICES_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_NATIVE_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_PURCHASING
-define:ENABLE_CLOUD_SERVICES_ANALYTICS
-define:ENABLE_CLOUD_SERVICES_BUILD
-define:ENABLE_EDITOR_GAME_SERVICES
-define:ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT
-define:ENABLE_CLOUD_LICENSE
-define:ENABLE_EDITOR_HUB_LICENSE
-define:ENABLE_WEBSOCKET_CLIENT
-define:ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API
-define:ENABLE_DIRECTOR_AUDIO
-define:ENABLE_DIRECTOR_TEXTURE
-define:ENABLE_MANAGED_JOBS
-define:ENABLE_MANAGED_TRANSFORM_JOBS
-define:ENABLE_MANAGED_ANIMATION_JOBS
-define:ENABLE_MANAGED_AUDIO_JOBS
-define:ENABLE_ENGINE_CODE_STRIPPING
-define:ENABLE_ONSCREEN_KEYBOARD
-define:ENABLE_MANAGED_UNITYTLS
-define:INCLUDE_DYNAMIC_GI
-define:ENABLE_SCRIPTING_GC_WBARRIERS
-define:PLATFORM_SUPPORTS_MONO
-define:ENABLE_MARSHALLING_TESTS
-define:ENABLE_VIDEO
-define:ENABLE_NAVIGATION_OFFMESHLINK_TO_NAVMESHLINK
-define:ENABLE_ACCELERATOR_CLIENT_DEBUGGING
-define:ENABLE_ACCESSIBILITY
-define:TEXTCORE_1_0_OR_NEWER
-define:EDITOR_ONLY_NAVMESH_BUILDER_DEPRECATED
-define:PLATFORM_ANDROID
-define:UNITY_ANDROID
-define:UNITY_ANDROID_API
-define:ENABLE_EGL
-define:ENABLE_NETWORK
-define:ENABLE_RUNTIME_GI
-define:ENABLE_CRUNCH_TEXTURE_COMPRESSION
-define:UNITY_CAN_SHOW_SPLASH_SCREEN
-define:UNITY_HAS_GOOGLEVR
-define:UNITY_HAS_TANGO
-define:ENABLE_SPATIALTRACKING
-define:ENABLE_ETC_COMPRESSION
-define:PLATFORM_EXTENDS_VULKAN_DEVICE
-define:PLATFORM_HAS_MULTIPLE_SWAPCHAINS
-define:UNITY_ANDROID_SUPPORTS_SHADOWFILES
-define:PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP
-define:PLATFORM_EXTENDS_VULKAN_PIPELINE_CACHE
-define:PLATFORM_SUPPORTS_SPLIT_GRAPHICS_JOBS
-define:PLATFORM_HAS_ADDITIONAL_API_CHECKS
-define:ENABLE_UNITYADS_RUNTIME
-define:UNITY_UNITYADS_API
-define:ENABLE_MONO
-define:NET_4_6
-define:NET_UNITY_4_8
-define:ENABLE_PROFILER
-define:DEBUG
-define:TRACE
-define:UNITY_ASSERTIONS
-define:UNITY_EDITOR
-define:UNITY_EDITOR_64
-define:UNITY_EDITOR_WIN
-define:ENABLE_UNITY_COLLECTIONS_CHECKS
-define:ENABLE_BURST_AOT
-define:UNITY_TEAM_LICENSE
-define:ENABLE_CUSTOM_RENDER_TEXTURE
-define:ENABLE_DIRECTOR
-define:ENABLE_LOCALIZATION
-define:ENABLE_SPRITES
-define:ENABLE_TERRAIN
-define:ENABLE_TILEMAP
-define:ENABLE_TIMELINE
-define:ENABLE_LEGACY_INPUT_MANAGER
-define:TEXTCORE_FONT_ENGINE_1_5_OR_NEWER
-define:TEXTCORE_TEXT_ENGINE_1_5_OR_NEWER
-define:ODIN_INSPECTOR
-define:ODIN_INSPECTOR_3
-define:ODIN_INSPECTOR_3_1
-define:DEVELOPMENT
-define:ENABLE_LOG
-define:AMPLIFY_SHADER_EDITOR
-define:CSHARP_7_OR_LATER
-define:CSHARP_7_3_OR_NEWER
-r:"Assets/Plugins/EPPlus.dll"
-r:"Assets/Plugins/LitJson.dll"
-r:"Assets/Plugins/protobuf-net.dll"
-r:"Assets/Plugins/Protobuf/Google.Protobuf.dll"
-r:"Assets/ThirdParty/Demigiant/DemiLib/DemiLib.dll"
-r:"Assets/ThirdParty/Demigiant/DemiLib/Editor/DemiEditor.dll"
-r:"Assets/ThirdParty/Demigiant/DOTween/DOTween.dll"
-r:"Assets/ThirdParty/Demigiant/DOTween/DOTween43.dll"
-r:"Assets/ThirdParty/Demigiant/DOTween/DOTween46.dll"
-r:"Assets/ThirdParty/Demigiant/DOTween/DOTween50.dll"
-r:"Assets/ThirdParty/Demigiant/DOTween/Editor/DOTweenEditor.dll"
-r:"Assets/ThirdParty/Demigiant/DOTweenPro/DOTweenPro.dll"
-r:"Assets/ThirdParty/Demigiant/DOTweenPro/Editor/DOTweenProEditor.dll"
-r:"Assets/ThirdParty/GameFramework/Libraries/ICSharpCode.SharpZipLib.dll"
-r:"Assets/ThirdParty/ParticleEffectForUGUI-main/Runtime/Coffee.UIParticle.R.dll"
-r:"Assets/ThirdParty/Plugins/Sirenix/Assemblies/Sirenix.OdinInspector.Attributes.dll"
-r:"Assets/ThirdParty/Plugins/Sirenix/Assemblies/Sirenix.OdinInspector.Editor.dll"
-r:"Assets/ThirdParty/Plugins/Sirenix/Assemblies/Sirenix.Reflection.Editor.dll"
-r:"Assets/ThirdParty/Plugins/Sirenix/Assemblies/Sirenix.Serialization.Config.dll"
-r:"Assets/ThirdParty/Plugins/Sirenix/Assemblies/Sirenix.Serialization.dll"
-r:"Assets/ThirdParty/Plugins/Sirenix/Assemblies/Sirenix.Utilities.dll"
-r:"Assets/ThirdParty/Plugins/Sirenix/Assemblies/Sirenix.Utilities.Editor.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEditor.Graphs.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.AccessibilityModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.AdaptivePerformanceModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.BuildProfileModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreBusinessMetricsModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.DeviceSimulatorModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.DiagnosticsModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.EditorToolbarModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.EmbreeModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.GIModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphicsStateCollectionSerializerModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphViewModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.GridAndSnapModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.GridModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.MultiplayerModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.Physics2DModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.PhysicsModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.PresetsUIModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.PropertiesModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.QuickSearchModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.SafeModeModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneViewModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.ShaderFoundryModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.SketchUpModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.SpriteMaskModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.SpriteShapeModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.SubstanceModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.TerrainModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreFontEngineModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreTextEngineModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.TextRenderingModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.TilemapModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.TreeModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.UIAutomationModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.UIBuilderModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.UmbraModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.VFXModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.VideoModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.XRModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AIModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AnimationModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ARModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AudioModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ClothModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ContentLoadModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.CoreModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.DirectorModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.GameCenterModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.GIModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.GraphicsStateCollectionSerializerModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.GridModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.HierarchyCoreModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.HotReloadModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.IMGUIModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.InputForUIModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.InputModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.LocalizationModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.MarshallingModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.MultiplayerModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.Physics2DModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.PhysicsModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.PropertiesModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ShaderVariantAnalyticsModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.StreamingModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SubstanceModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreFontEngineModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreTextEngineModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TilemapModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TLSModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UIModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UmbraModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.VehiclesModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.VFXModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.VideoModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.VRModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.WindModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.XRModule.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/Unity.Android.Gradle.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/Unity.Android.Types.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/Microsoft.Win32.Primitives.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/netstandard.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.AppContext.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Buffers.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.Concurrent.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.NonGeneric.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.Specialized.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.Annotations.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.EventBasedAsync.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.Primitives.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.TypeConverter.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Console.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Data.Common.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Contracts.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Debug.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.FileVersionInfo.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Process.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.StackTrace.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.TextWriterTraceListener.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Tools.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.TraceSource.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Drawing.Primitives.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Dynamic.Runtime.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.Calendars.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.Extensions.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.Compression.ZipFile.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.DriveInfo.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.Primitives.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.Watcher.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.IsolatedStorage.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.MemoryMappedFiles.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.Pipes.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.UnmanagedMemoryStream.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Expressions.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Parallel.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Queryable.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Memory.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Http.Rtc.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.NameResolution.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.NetworkInformation.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Ping.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Primitives.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Requests.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Security.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Sockets.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebHeaderCollection.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebSockets.Client.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebSockets.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ObjectModel.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.DispatchProxy.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.ILGeneration.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.Lightweight.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Extensions.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Primitives.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.Reader.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.ResourceManager.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.Writer.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.CompilerServices.VisualC.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Extensions.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Handles.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.RuntimeInformation.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.WindowsRuntime.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Numerics.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Formatters.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Json.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Primitives.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Xml.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Claims.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Algorithms.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Csp.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Encoding.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Primitives.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.X509Certificates.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Principal.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.SecureString.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Duplex.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Http.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.NetTcp.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Primitives.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Security.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.Encoding.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.Encoding.Extensions.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.RegularExpressions.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Overlapped.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.Extensions.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.Parallel.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Thread.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.ThreadPool.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Timer.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ValueTuple.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.ReaderWriter.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XDocument.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XmlDocument.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XmlSerializer.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XPath.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XPath.XDocument.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Microsoft.CSharp.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/mscorlib.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.ComponentModel.Composition.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Core.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Data.DataSetExtensions.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Data.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Drawing.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.IO.Compression.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.IO.Compression.FileSystem.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Net.Http.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Numerics.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Numerics.Vectors.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Runtime.Serialization.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Transactions.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Xml.dll"
-r:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Xml.Linq.dll"
-r:"Library/PackageCache/com.unity.collab-proxy/Lib/Editor/PlasticSCM/log4netPlastic.dll"
-r:"Library/PackageCache/com.unity.collab-proxy/Lib/Editor/PlasticSCM/Unity.Plastic.Antlr3.Runtime.dll"
-r:"Library/PackageCache/com.unity.collab-proxy/Lib/Editor/PlasticSCM/Unity.Plastic.Newtonsoft.Json.dll"
-r:"Library/PackageCache/com.unity.collab-proxy/Lib/Editor/PlasticSCM/unityplastic.dll"
-r:"Library/PackageCache/com.unity.collections/Unity.Collections.LowLevel.ILSupport/Unity.Collections.LowLevel.ILSupport.dll"
-r:"Library/PackageCache/com.unity.ext.nunit/net40/unity-custom/nunit.framework.dll"
-r:"Library/PackageCache/com.unity.nuget.mono-cecil/Mono.Cecil.dll"
-r:"Library/PackageCache/com.unity.nuget.newtonsoft-json/Runtime/Newtonsoft.Json.dll"
-r:"Library/PackageCache/com.unity.visualscripting/Editor/VisualScripting.Core/Dependencies/DotNetZip/Unity.VisualScripting.IonicZip.dll"
-r:"Library/PackageCache/com.unity.visualscripting/Editor/VisualScripting.Core/Dependencies/YamlDotNet/Unity.VisualScripting.YamlDotNet.dll"
-r:"Library/PackageCache/com.unity.visualscripting/Editor/VisualScripting.Core/EditorAssetResources/Unity.VisualScripting.TextureAssets.dll"
-r:"Library/PackageCache/com.unity.visualscripting/Runtime/VisualScripting.Flow/Dependencies/NCalc/Unity.VisualScripting.Antlr3.Runtime.dll"
-r:"Packages/com.bezi.sidekick/Editor/BeziSidekickInternal.dll"
-r:"Packages/com.bezi.sidekick/Plugins/bezi-websocket-sharp.dll"
-r:"Packages/com.bezi.sidekick/Plugins/Microsoft.CodeAnalysis.CSharp.dll"
-r:"Packages/com.bezi.sidekick/Plugins/Microsoft.CodeAnalysis.dll"
-r:"Packages/com.bezi.sidekick/Plugins/Sentry.dll"
-r:"Packages/com.bezi.sidekick/Plugins/Sentry.Microsoft.Bcl.AsyncInterfaces.dll"
-r:"Packages/com.bezi.sidekick/Plugins/Sentry.System.Buffers.dll"
-r:"Packages/com.bezi.sidekick/Plugins/Sentry.System.Collections.Immutable.dll"
-r:"Packages/com.bezi.sidekick/Plugins/Sentry.System.Memory.dll"
-r:"Packages/com.bezi.sidekick/Plugins/Sentry.System.Numerics.Vectors.dll"
-r:"Packages/com.bezi.sidekick/Plugins/Sentry.System.Reflection.Metadata.dll"
-r:"Packages/com.bezi.sidekick/Plugins/Sentry.System.Runtime.CompilerServices.Unsafe.dll"
-r:"Packages/com.bezi.sidekick/Plugins/Sentry.System.Text.Encodings.Web.dll"
-r:"Packages/com.bezi.sidekick/Plugins/Sentry.System.Text.Json.dll"
-r:"Packages/com.bezi.sidekick/Plugins/Sentry.System.Threading.Tasks.Extensions.dll"
-r:"Packages/com.bezi.sidekick/Plugins/SharpToken.dll"
-r:"Packages/com.bezi.sidekick/Plugins/System.Collections.Immutable.dll"
-r:"Packages/com.bezi.sidekick/Plugins/System.Reflection.Metadata.dll"
-r:"Packages/com.bezi.sidekick/Plugins/System.Runtime.CompilerServices.Unsafe.dll"
-r:"Packages/com.code-philosophy.hybridclr/Plugins/dnlib.dll"
-r:"Packages/com.code-philosophy.hybridclr/Plugins/LZ4.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/FancyScrollView.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Game.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/GameFramework.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/IngameDebugConsole.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Mosframe.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/spine-csharp.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/UIEffect.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.UI.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.UI.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/UnityGameFramework.Runtime.ref.dll"
-analyzer:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Tools/Unity.SourceGenerators/Unity.Properties.SourceGenerator.dll"
-analyzer:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Tools/Unity.SourceGenerators/Unity.SourceGenerators.dll"
-analyzer:"D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Tools/Unity.SourceGenerators/Unity.UIToolkit.SourceGenerator.dll"
"Assets/Scripts/HotUpdate/AppDomain.cs"
"Assets/Scripts/HotUpdate/Game/Entity/EntityData/ED_BuidlingPreview.cs"
"Assets/Scripts/HotUpdate/Game/Entity/EntityData/ED_Building.cs"
"Assets/Scripts/HotUpdate/Game/Entity/EntityData/ED_BuildingDisplay.cs"
"Assets/Scripts/HotUpdate/Game/Entity/EntityData/ED_CommonDisplay.cs"
"Assets/Scripts/HotUpdate/Game/Entity/EntityData/ED_SoldierDisplay.cs"
"Assets/Scripts/HotUpdate/Game/Entity/EntityData/ED_Wall.cs"
"Assets/Scripts/HotUpdate/Game/Entity/EntityLogic/EL_AreaDecoration.cs"
"Assets/Scripts/HotUpdate/Game/Entity/EntityLogic/EL_AreaUnlock.cs"
"Assets/Scripts/HotUpdate/Game/Entity/EntityLogic/EL_BattleBullet.cs"
"Assets/Scripts/HotUpdate/Game/Entity/EntityLogic/EL_BattleEffect.cs"
"Assets/Scripts/HotUpdate/Game/Entity/EntityLogic/EL_BattleHero.cs"
"Assets/Scripts/HotUpdate/Game/Entity/EntityLogic/EL_BattleUnitBase.cs"
"Assets/Scripts/HotUpdate/Game/Entity/EntityLogic/EL_BuidlingPreview.cs"
"Assets/Scripts/HotUpdate/Game/Entity/EntityLogic/EL_BuidlingPreview/BuildingGridDisplay.cs"
"Assets/Scripts/HotUpdate/Game/Entity/EntityLogic/EL_Building.cs"
"Assets/Scripts/HotUpdate/Game/Entity/EntityLogic/EL_BuildingDisplay.cs"
"Assets/Scripts/HotUpdate/Game/Entity/EntityLogic/EL_BuildingDisplaySoldier.cs"
"Assets/Scripts/HotUpdate/Game/Entity/EntityLogic/EL_BuildingEvent.cs"
"Assets/Scripts/HotUpdate/Game/Entity/EntityLogic/EL_BuildingTeam.cs"
"Assets/Scripts/HotUpdate/Game/Entity/EntityLogic/EL_BuildingTech.cs"
"Assets/Scripts/HotUpdate/Game/Entity/EntityLogic/EL_CommonDisplay.cs"
"Assets/Scripts/HotUpdate/Game/Entity/EntityLogic/EL_CommonEffect.cs"
"Assets/Scripts/HotUpdate/Game/Entity/EntityLogic/EL_PvePath.cs"
"Assets/Scripts/HotUpdate/Game/Entity/EntityLogic/EL_PvePathAgent.cs"
"Assets/Scripts/HotUpdate/Game/Entity/EntityLogic/EL_PvePathAgentDisplay.cs"
"Assets/Scripts/HotUpdate/Game/Entity/EntityLogic/EL_PvePathGroundDisplay.cs"
"Assets/Scripts/HotUpdate/Game/Entity/EntityLogic/EL_PvePathObjDisplay.cs"
"Assets/Scripts/HotUpdate/Game/Entity/EntityLogic/EL_SoldierDisplay.cs"
"Assets/Scripts/HotUpdate/Game/Entity/EntityLogic/EL_TownMovePreview.cs"
"Assets/Scripts/HotUpdate/Game/Entity/EntityLogic/EL_Train.cs"
"Assets/Scripts/HotUpdate/Game/Entity/EntityLogic/EL_TrainStation.cs"
"Assets/Scripts/HotUpdate/Game/Entity/EntityLogic/EL_Wall.cs"
"Assets/Scripts/HotUpdate/Game/Entity/EntityLogic/EL_WorldMapCity.cs"
"Assets/Scripts/HotUpdate/Game/Entity/EntityLogic/EL_WorldMapDisplay.cs"
"Assets/Scripts/HotUpdate/Game/Entity/EntityLogic/EL_WorldMapElement.cs"
"Assets/Scripts/HotUpdate/Game/Entity/EntityLogic/EL_WorldMapGridClick.cs"
"Assets/Scripts/HotUpdate/Game/Entity/EntityLogic/EL_WorldMapTown.cs"
"Assets/Scripts/HotUpdate/Game/Entity/EntityLogic/EL_WorldMapTroop.cs"
"Assets/Scripts/HotUpdate/Game/Entity/EntityLogic/EL_WorldMapUnBuildArea.cs"
"Assets/Scripts/HotUpdate/Game/GameDefine.cs"
"Assets/Scripts/HotUpdate/Game/HUD/HUDBuildingBuzy.cs"
"Assets/Scripts/HotUpdate/Game/HUD/HUDBuildingDispatch.cs"
"Assets/Scripts/HotUpdate/Game/HUD/HUDClaimResource.cs"
"Assets/Scripts/HotUpdate/Game/HUD/HUDEquipment.cs"
"Assets/Scripts/HotUpdate/Game/HUD/HUDOutfire.cs"
"Assets/Scripts/HotUpdate/Game/HUD/HUDProduceResource.cs"
"Assets/Scripts/HotUpdate/Game/HUD/HUDPvePathLock.cs"
"Assets/Scripts/HotUpdate/Game/HUD/HUDRepair.cs"
"Assets/Scripts/HotUpdate/Game/HUD/HUDShop.cs"
"Assets/Scripts/HotUpdate/Game/HUD/HUDSoldierTrain.cs"
"Assets/Scripts/HotUpdate/Game/HUD/HUDSoldierTreat.cs"
"Assets/Scripts/HotUpdate/Game/HUD/HUDTech.cs"
"Assets/Scripts/HotUpdate/Game/HUD/HUDTrainContract.cs"
"Assets/Scripts/HotUpdate/Game/HUD/HUDTrainPassenger.cs"
"Assets/Scripts/HotUpdate/Game/HUD/HUDUav.cs"
"Assets/Scripts/HotUpdate/Game/HUD/HUDUnionHelp.cs"
"Assets/Scripts/HotUpdate/Game/HUD/HUDWorldMapGridClick.cs"
"Assets/Scripts/HotUpdate/Game/Manager/ArenaManager.cs"
"Assets/Scripts/HotUpdate/Game/Manager/BagData.cs"
"Assets/Scripts/HotUpdate/Game/Manager/BagManager.cs"
"Assets/Scripts/HotUpdate/Game/Manager/Battle5v5Data.cs"
"Assets/Scripts/HotUpdate/Game/Manager/Battle5v5Data/Battle5v5ParamBase.cs"
"Assets/Scripts/HotUpdate/Game/Manager/Battle5v5Data/Battle5v5ParamDebug.cs"
"Assets/Scripts/HotUpdate/Game/Manager/Battle5v5Data/Battle5v5ParamDungeon.cs"
"Assets/Scripts/HotUpdate/Game/Manager/Battle5v5Data/Battle5v5ParamTradeTruck.cs"
"Assets/Scripts/HotUpdate/Game/Manager/BezierCurve.cs"
"Assets/Scripts/HotUpdate/Game/Manager/BuildingData.cs"
"Assets/Scripts/HotUpdate/Game/Manager/ChaoZhiData.cs"
"Assets/Scripts/HotUpdate/Game/Manager/DungeonData.cs"
"Assets/Scripts/HotUpdate/Game/Manager/EquipmentData.cs"
"Assets/Scripts/HotUpdate/Game/Manager/FlyResManager.cs"
"Assets/Scripts/HotUpdate/Game/Manager/FlyResManager/IUIResIconLocator.cs"
"Assets/Scripts/HotUpdate/Game/Manager/GeneralShopData.cs"
"Assets/Scripts/HotUpdate/Game/Manager/HeroData.cs"
"Assets/Scripts/HotUpdate/Game/Manager/MailData.cs"
"Assets/Scripts/HotUpdate/Game/Manager/MainCityAreaData.cs"
"Assets/Scripts/HotUpdate/Game/Manager/MainCityGridData.cs"
"Assets/Scripts/HotUpdate/Game/Manager/MallData.cs"
"Assets/Scripts/HotUpdate/Game/Manager/NoviceTrainingData.cs"
"Assets/Scripts/HotUpdate/Game/Manager/PaymentData.cs"
"Assets/Scripts/HotUpdate/Game/Manager/PeakRankData.cs"
"Assets/Scripts/HotUpdate/Game/Manager/PrivilegeData.cs"
"Assets/Scripts/HotUpdate/Game/Manager/PvePathData.cs"
"Assets/Scripts/HotUpdate/Game/Manager/QueueData.cs"
"Assets/Scripts/HotUpdate/Game/Manager/RecruitData.cs"
"Assets/Scripts/HotUpdate/Game/Manager/RedPointManager.cs"
"Assets/Scripts/HotUpdate/Game/Manager/RobotManager.cs"
"Assets/Scripts/HotUpdate/Game/Manager/RoleData.cs"
"Assets/Scripts/HotUpdate/Game/Manager/SoldierData.cs"
"Assets/Scripts/HotUpdate/Game/Manager/SurvivorData.cs"
"Assets/Scripts/HotUpdate/Game/Manager/TaskData.cs"
"Assets/Scripts/HotUpdate/Game/Manager/TeamData.cs"
"Assets/Scripts/HotUpdate/Game/Manager/TechData.cs"
"Assets/Scripts/HotUpdate/Game/Manager/TradeTruckData.cs"
"Assets/Scripts/HotUpdate/Game/Manager/UAVData.cs"
"Assets/Scripts/HotUpdate/Game/Manager/UnionData.cs"
"Assets/Scripts/HotUpdate/Game/Manager/UserData.cs"
"Assets/Scripts/HotUpdate/Game/Manager/VipData.cs"
"Assets/Scripts/HotUpdate/Game/Manager/WorldMapData.cs"
"Assets/Scripts/HotUpdate/Game/Manager/WorldMapData/TroopData.cs"
"Assets/Scripts/HotUpdate/Game/Manager/WorldMapData/WorldMapGridData.cs"
"Assets/Scripts/HotUpdate/Game/Manager/WorldMapData/WorldMapTroopData.cs"
"Assets/Scripts/HotUpdate/Game/Module/BuildingModule.cs"
"Assets/Scripts/HotUpdate/Game/Module/BuildingModules/BuildingExp.cs"
"Assets/Scripts/HotUpdate/Game/Module/BuildingModules/BuildingGold.cs"
"Assets/Scripts/HotUpdate/Game/Module/BuildingModules/BuildingGrain.cs"
"Assets/Scripts/HotUpdate/Game/Module/BuildingModules/BuildingIron.cs"
"Assets/Scripts/HotUpdate/Game/Module/BuildingModules/BuildingSoldierTraining.cs"
"Assets/Scripts/HotUpdate/Game/Module/BuildingModules/EnumBuildingMenu.cs"
"Assets/Scripts/HotUpdate/Game/Module/BuildQueueMoudle.cs"
"Assets/Scripts/HotUpdate/Game/Module/EquipmentModule.cs"
"Assets/Scripts/HotUpdate/Game/Module/HeroModule.cs"
"Assets/Scripts/HotUpdate/Game/Module/ItemModule.cs"
"Assets/Scripts/HotUpdate/Game/Module/MailModule.cs"
"Assets/Scripts/HotUpdate/Game/Module/MainCityAreaModule.cs"
"Assets/Scripts/HotUpdate/Game/Module/MainCityWallModule.cs"
"Assets/Scripts/HotUpdate/Game/Module/PrivilegeModule.cs"
"Assets/Scripts/HotUpdate/Game/Module/PvePathModule.cs"
"Assets/Scripts/HotUpdate/Game/Module/QueueModule.cs"
"Assets/Scripts/HotUpdate/Game/Module/QueueModules/ConstructionQueue.cs"
"Assets/Scripts/HotUpdate/Game/Module/QueueModules/EquipmentQueue.cs"
"Assets/Scripts/HotUpdate/Game/Module/QueueModules/ResourceQueue.cs"
"Assets/Scripts/HotUpdate/Game/Module/QueueModules/SoldierQueue.cs"
"Assets/Scripts/HotUpdate/Game/Module/QueueModules/SoldierTreatQueue.cs"
"Assets/Scripts/HotUpdate/Game/Module/QueueModules/TechQueue.cs"
"Assets/Scripts/HotUpdate/Game/Module/QueueModules/UpgradingQueue.cs"
"Assets/Scripts/HotUpdate/Game/Module/RecruitModule.cs"
"Assets/Scripts/HotUpdate/Game/Module/RedPointModule.cs"
"Assets/Scripts/HotUpdate/Game/Module/SoldierModule.cs"
"Assets/Scripts/HotUpdate/Game/Module/SurvivorMoudle.cs"
"Assets/Scripts/HotUpdate/Game/Module/UAVModule.cs"
"Assets/Scripts/HotUpdate/Game/Module/UIItemModule.cs"
"Assets/Scripts/HotUpdate/Game/Proto/Account.cs"
"Assets/Scripts/HotUpdate/Game/Proto/Activity.cs"
"Assets/Scripts/HotUpdate/Game/Proto/Arena.cs"
"Assets/Scripts/HotUpdate/Game/Proto/Article.cs"
"Assets/Scripts/HotUpdate/Game/Proto/Battle.cs"
"Assets/Scripts/HotUpdate/Game/Proto/Build.cs"
"Assets/Scripts/HotUpdate/Game/Proto/Camera.cs"
"Assets/Scripts/HotUpdate/Game/Proto/City.cs"
"Assets/Scripts/HotUpdate/Game/Proto/Common.cs"
"Assets/Scripts/HotUpdate/Game/Proto/Dungeon.cs"
"Assets/Scripts/HotUpdate/Game/Proto/Equip.cs"
"Assets/Scripts/HotUpdate/Game/Proto/Fight.cs"
"Assets/Scripts/HotUpdate/Game/Proto/Forward.cs"
"Assets/Scripts/HotUpdate/Game/Proto/Gameconfig.cs"
"Assets/Scripts/HotUpdate/Game/Proto/Gate.cs"
"Assets/Scripts/HotUpdate/Game/Proto/Gift.cs"
"Assets/Scripts/HotUpdate/Game/Proto/Gm.cs"
"Assets/Scripts/HotUpdate/Game/Proto/Hero.cs"
"Assets/Scripts/HotUpdate/Game/Proto/Innercity.cs"
"Assets/Scripts/HotUpdate/Game/Proto/Item.cs"
"Assets/Scripts/HotUpdate/Game/Proto/Lang.cs"
"Assets/Scripts/HotUpdate/Game/Proto/Mail.cs"
"Assets/Scripts/HotUpdate/Game/Proto/Mine.cs"
"Assets/Scripts/HotUpdate/Game/Proto/Monster.cs"
"Assets/Scripts/HotUpdate/Game/Proto/Novice.cs"
"Assets/Scripts/HotUpdate/Game/Proto/Payment.cs"
"Assets/Scripts/HotUpdate/Game/Proto/Peak.cs"
"Assets/Scripts/HotUpdate/Game/Proto/Privilege.cs"
"Assets/Scripts/HotUpdate/Game/Proto/Protocol.cs"
"Assets/Scripts/HotUpdate/Game/Proto/Push.cs"
"Assets/Scripts/HotUpdate/Game/Proto/Rank.cs"
"Assets/Scripts/HotUpdate/Game/Proto/Recruit.cs"
"Assets/Scripts/HotUpdate/Game/Proto/Role.cs"
"Assets/Scripts/HotUpdate/Game/Proto/Roledata.cs"
"Assets/Scripts/HotUpdate/Game/Proto/Shop.cs"
"Assets/Scripts/HotUpdate/Game/Proto/Soldier.cs"
"Assets/Scripts/HotUpdate/Game/Proto/Source.cs"
"Assets/Scripts/HotUpdate/Game/Proto/Store.cs"
"Assets/Scripts/HotUpdate/Game/Proto/Survivor.cs"
"Assets/Scripts/HotUpdate/Game/Proto/Task.cs"
"Assets/Scripts/HotUpdate/Game/Proto/Team.cs"
"Assets/Scripts/HotUpdate/Game/Proto/Tech.cs"
"Assets/Scripts/HotUpdate/Game/Proto/Town.cs"
"Assets/Scripts/HotUpdate/Game/Proto/Trade.cs"
"Assets/Scripts/HotUpdate/Game/Proto/Troop.cs"
"Assets/Scripts/HotUpdate/Game/Proto/Uav.cs"
"Assets/Scripts/HotUpdate/Game/Proto/Union.cs"
"Assets/Scripts/HotUpdate/Game/Proto/Vip.cs"
"Assets/Scripts/HotUpdate/Game/Proto/Wmcamera.cs"
"Assets/Scripts/HotUpdate/Game/Proto/Worker.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIChildForm/ChaoZhi/ChaoZhiLogic.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIChildForm/ChaoZhi/ChaoZhi_DailyRecharge.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIChildForm/ChaoZhi/ChaoZhi_RoleRecharge.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIChildForm/ChaoZhi/ChaoZhi_RoleUpStar.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIChildForm/ChaoZhi/ChaoZhi_WarriorZL.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIChildForm/ChaoZhi/ChaoZhi_WeeklyRecharge.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIChildForm/GeneralShop/GeneralShopLogic.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIChildForm/GeneralShop/GeneralShop_Diamond.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIChildForm/GeneralShop/GeneralShop_Expedition.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIChildForm/GeneralShop/GeneralShop_Honor.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIChildForm/GeneralShop/GeneralShop_League.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIChildForm/GeneralShop/GeneralShop_Season.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIChildForm/GeneralShop/GeneralShop_Vip.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIChildForm/Mail/MailDetail_OnlyStr.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIChildForm/Mail/MailDetail_President.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIChildForm/Mail/MailDetail_Recharge.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIChildForm/Mail/MailDetail_Reward.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIChildForm/Mail/MailDetail_SystemNotice.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIChildForm/Mail/MailLogic.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIChildForm/Mail/Mail_ActivityItem.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIChildForm/Mail/Mail_BattleItem.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIChildForm/Mail/Mail_CollectItem.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIChildForm/Mail/Mail_DailyItem.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIChildForm/Mail/Mail_LeagueItem.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIChildForm/Mail/Mail_PurchaseItem.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIChildForm/Mail/Mail_SeasonItem.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIChildForm/Mail/Mail_SystemItem.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIChildForm/Mall/Mall_DailyBuy.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIChildForm/Mall/Mall_DailyDeal.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIChildForm/Mall/Mall_DiamondShop.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIChildForm/Mall/Mall_GiftShop.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIChildForm/Mall/Mall_GrowthFund.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIChildForm/Mall/Mall_MonthCard.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIChildForm/Mall/Mall_WeeklyCard.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIChildForm/Mall/Mall_WeeklyDeal.cs"
"Assets/Scripts/HotUpdate/Game/UI/UICommon/RechargeScore.cs"
"Assets/Scripts/HotUpdate/Game/UI/UICommon/RechargeScoreCtrl.cs"
"Assets/Scripts/HotUpdate/Game/UI/UICommon/UIBackgroundScroller.cs"
"Assets/Scripts/HotUpdate/Game/UI/UICommon/UIHeroItem.cs"
"Assets/Scripts/HotUpdate/Game/UI/UICommon/UIRedPointItem.cs"
"Assets/Scripts/HotUpdate/Game/UI/UICommon/UIRelationItem.cs"
"Assets/Scripts/HotUpdate/Game/UI/UICommon/UITrainItem.cs"
"Assets/Scripts/HotUpdate/Game/UI/UICommon/UITruckItem.cs"
"Assets/Scripts/HotUpdate/Game/UI/UICommon/WorldMapMenuItem.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/MailDetail_TradeTruck.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/MenuForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIAccountManageForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIAccountManageFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIArenaChallengeForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIArenaChallengeFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIBagForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIBagFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIBagStatistics.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIBagStatisticsBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIBattle5v5Choose.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIBattle5v5Choose/UIBattle5v5ChooseDebug.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIBattle5v5Choose/UIBattle5v5ChooseHUD.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIBattle5v5Choose/UIBattle5v5ChooseTeamSwitch.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIBattle5v5ChooseBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIBattle5v5Debug.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIBattle5v5DebugBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIBattle5V5DungeonDefeat.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIBattle5V5DungeonDefeatBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIBattle5V5DungeonStatsForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIBattle5V5DungeonStatsFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIBattle5V5DungeonVictory.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIBattle5V5DungeonVictoryBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIBattle5V5Fight.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIBattle5V5Fight/UIBattle5V5FightHeroBlood.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIBattle5V5Fight/UIBattle5V5FightHeroItem.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIBattle5V5Fight/UIBattle5V5FightHurtNumber.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIBattle5V5FightBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIBuildingCompleteForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIBuildingCompleteFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIBuildingDetailForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIBuildingDetailFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIBuildingDispatchForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIBuildingDispatchFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIBuildingFireForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIBuildingFireFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIBuildingGround.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIBuildingGroundBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIBuildingMaxLevelForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIBuildingMaxLevelFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIBuildingMenuForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIBuildingMenuFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIBuildingMoveForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIBuildingMoveFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIBuildingQueueForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIBuildingQueueFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIBuildingSpeedUpForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIBuildingSpeedUpFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIBuildingSurvivorForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIBuildingSurvivorFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIBuildingUAVForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIBuildingUAVFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIBuildingWagonForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIBuildingWagonFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIBuildingWagonRewardForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIBuildingWagonRewardFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIBuildingWagonTragetForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIBuildingWagonTragetFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIBuildingWallForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIBuildingWallFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIChallengeBuyForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIChallengeBuyFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIChangeMailForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIChangeMailFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIChaoZhiActivityForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIChaoZhiActivityFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIChooseBoxForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIChooseBoxFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIClickEffectForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIClickEffectFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIComDescForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIComDescFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UICommonConfirmForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UICommonConfirmFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UICommonSpeedUpItemForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UICommonSpeedUpItemFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UICommonTipForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UICommonTipFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIDialogForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIDispatchListForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIDispatchListFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIEmailForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIEmailFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIEquipmentDetailForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIEquipmentDetailFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIEquipmentFactoryForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIEquipmentFactoryFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIEquipmentPromoteForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIEquipmentPromoteFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIEquipmentReplaceForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIEquipmentReplaceFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIFightChangeForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIFightChangeFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIFlyTextForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIFlyTextFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIGeneralBuyForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIGeneralBuyFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIGeneralShopForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIGeneralShopFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIGetWayForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIGetWayFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIGrowthFundBuyForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIGrowthFundBuyFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIHeroAttrDetailForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIHeroAttrDetailFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIHeroAttrPanel.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIHeroAttrPanelBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIHeroDevelopForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIHeroDevelopFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIHeroForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIHeroFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIHeroHonorForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIHeroHonorFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIHeroHonorUpgradeForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIHeroHonorUpgradeFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIHeroNewForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIHeroNewFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIHeroPoolUpdateForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIHeroPoolUpdateFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIHeroSkillPanel.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIHeroSkillPanelBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIHeroSkillTipForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIHeroSkillTipFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIHeroSkillUnlockForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIHeroSkillUnlockFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIHeroUpStarPanel.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIHeroUpStarPanelBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIItemBoxProForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIItemBoxProFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIItemTextTip.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIItemTextTipBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UILoadingForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UILoadingFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UILockForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UILockFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UILoginForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UILoginFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIMailDetailForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIMailDetailFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIMailForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIMailFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIMailMsgListForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIMailMsgListFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIMainFaceBuildForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIMainFaceBuildFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIMainFaceForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIMainFaceFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIMainMenuForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIMainMenuFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIMallForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIMallFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIMonthWeekCardForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIMonthWeekCardFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UINovicReportForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UINovicReportFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UINovicRewardForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UINovicRewardFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIOptionsForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIPaymentForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIPaymentFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIPeakArenaForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIPeakArenaFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIPeakRewardForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIPeakRewardFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIPlayerDetailForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIPlayerDetailFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIPlayerInfoForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIPlayerInfoFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIPlayerRecordForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIPlayerRecordFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIPlayerSettingsForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIPlayerSettingsFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIPvePathInfoForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIPvePathInfoFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIRecruitAnimForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIRecruitAnimFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIRecruitForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIRecruitFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIRecruitHundredForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIRecruitHundredFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIRecruitRatioForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIRecruitRatioFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIResFlyForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIResFlyFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIRewardGetForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIRewardGetFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIRewardPreviewForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIRewardPreviewFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UISelectDailyDealForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UISelectDailyDealFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UISelectFinalHeroForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UISelectFinalHeroFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIServerListForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIServerListFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UISoldiersTrainUpForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UISoldiersTrainUpFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UISurvivorUpStarGradeForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UISurvivorUpStarGradeFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UISwitchAccountForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UISwitchAccountFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UITaskForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UITaskFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UITaskRewardForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UITaskRewardFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UITeamForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UITeamForm/UITeamFormHero.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UITeamForm/UITeamFormHeroContainer.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UITeamForm/UITeamFormHUD.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UITeamForm/UITeamFormNormalTeamSwitch.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UITeamFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UITechForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UITechFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UITechingForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UITechingFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UITechLevelBuffForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UITechLevelBuffFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UITechUpDetailForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UITechUpDetailFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UITownMoveForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UITownMoveFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UITradeTrainBattlePlanForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UITradeTrainBattlePlanFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UITradeTrainBattleRecordForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UITradeTrainBattleRecordFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UITradeTrainBattleResultForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UITradeTrainBattleResultFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UITradeTrainContractForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UITradeTrainContractFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UITradeTrainDescForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UITradeTrainDescFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UITradeTrainDetailForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UITradeTrainDetailFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UITradeTrainInviteConductorForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UITradeTrainInviteConductorFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UITradeTrainPassengerForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UITradeTrainPassengerFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UITradeTrainProbabilityForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UITradeTrainProbabilityFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UITradeTrainStationPlatformForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UITradeTrainStationPlatformFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UITradeTrainThanksForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UITradeTrainThanksFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UITradeTrainThanksListForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UITradeTrainThanksListFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UITradeTrainVIPDescForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UITradeTrainVIPDescFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UITradeTrainVIPForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UITradeTrainVIPFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UITradeTrainVIPInviteForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UITradeTrainVIPInviteFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UITradeTruckDepartForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UITradeTruckDepartFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UITradeTruckDescForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UITradeTruckDescFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UITradeTruckDetailForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UITradeTruckDetailFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UITradeTruckForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UITradeTruckFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UITradeTruckHistoryForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UITradeTruckHistoryFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UITradeTruckPlunderForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UITradeTruckPlunderFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UITrainingSoldiersForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UITrainingSoldiersFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UITreatSoldiersForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UITreatSoldiersFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIUAVAttributeTipForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIUAVAttributeTipFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIUAVComponentPreviewForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIUAVComponentPreviewFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIUAVComponentUpForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIUAVComponentUpFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIUAVLevelUpPreViewForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIUAVLevelUpPreViewFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIUAVSkillTip.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIUAVSkillTipBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIUavTip.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIUavTipBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIUnionApplyForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIUnionApplyFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIUnionCreateForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIUnionCreateFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIUnionEditChangeForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIUnionEditChangeFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIUnionEditForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIUnionEditFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIUnionExitWarnForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIUnionExitWarnFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIUnionFlagForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIUnionFlagFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIUnionForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIUnionFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIUnionFristForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIUnionFristFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIUnionGiftForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIUnionGiftFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIUnionGiftRatioForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIUnionGiftRatioFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIUnionHelpForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIUnionHelpFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIUnionHelpTipForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIUnionHelpTipFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIUnionInfoForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIUnionInfoFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIUnionLanguageForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIUnionLanguageFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIUnionListForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIUnionListFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIUnionManageForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIUnionManageFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIUnionMemberForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIUnionMemberFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIUnionMileStoneForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIUnionMileStoneFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIUnionPermissionForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIUnionPermissionFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIUnionRankForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIUnionRankFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIUnionRankRewardForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIUnionRankRewardFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIUnionRecordForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIUnionRecordFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIUnionSettingForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIUnionSettingFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIUnionShareForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIUnionShareFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIUnionShareSureForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIUnionShareSureFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIUnionStepUpForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIUnionStepUpFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIUnionTechForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIUnionTechFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIUnionTechUpForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIUnionTechUpFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIUnLockBuildingQueueForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIUnLockBuildingQueueFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIUseSpeedUpItemForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIUseSpeedUpItemFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIVipForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIVipFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIVipPointBuyForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIVipPointBuyFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIVipPointTipForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIVipPointTipFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIVipTimeBuyForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIVipTimeBuyFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIVipUpgradeForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIVipUpgradeFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIWorldMapDoomEliteMenu.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIWorldMapDoomEliteMenuBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIWorldMapGatherMenu.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIWorldMapGatherMenuBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIWorldMapMenuBase.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIWorldMapPreview.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIWorldMapPreviewBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIWorldMapPreviewLow.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIWorldMapPreviewLow/WmpCityDrawer.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIWorldMapPreviewLow/WmpCityUiHud.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIWorldMapPreviewLow/WmpDrawerBase.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIWorldMapPreviewLow/WmpElementDrawer.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIWorldMapPreviewLow/WmpElementUiHud.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIWorldMapPreviewLow/WmpMyTownDrawer.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIWorldMapPreviewLow/WmpMyTownUiHud.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIWorldMapPreviewLow/WmpTownDrawer.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIWorldMapPreviewLow/WmpTownUiHud.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIWorldMapPreviewLow/WmpUiHudBase.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIWorldMapPreviewLowBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIWorldMapSelectionConfirm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIWorldMapSelectionConfirmBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIWorldMapTeamSelectForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIWorldMapTeamSelectFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIWorldMapTownMenu.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIWorldMapTownMenuBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIWorldMapZombiesMenu.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIWorldMapZombiesMenuBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIZhanLingBuyForm.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIForm/UIZhanLingBuyFormBind.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIOpenParam/BuildingParams.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIOpenParam/DialogParams.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIOpenParam/EquipmentParams.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIOpenParam/FlyTextParams.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIOpenParam/HeroParams.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIOpenParam/PaymentParams.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIOpenParam/RecruitParams.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIOpenParam/RewardGetParams.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIOpenParam/SoldierParams.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIOpenParam/SurvivorParams.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIOpenParam/TradeTruckParams.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIOpenParam/UavParams.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIOpenParam/UiMainfaceParams.cs"
"Assets/Scripts/HotUpdate/Game/UI/UIOpenParam/UnionParams.cs"
"Assets/Scripts/HotUpdate/Game/Utils/AnimationEventHandler.cs"
"Assets/Scripts/HotUpdate/Game/Utils/EventDispatch.cs"
"Assets/Scripts/HotUpdate/Game/Utils/MapGridUtils.cs"
"Assets/Scripts/HotUpdate/Game/Utils/MeshColliderGenerator.cs"
"Assets/Scripts/HotUpdate/Game/Utils/WallFinder.cs"
"Assets/Scripts/HotUpdate/GameFramework/Base/GameEntry.Builtin.cs"
"Assets/Scripts/HotUpdate/GameFramework/Base/GameEntry.Custom.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/BaseSceneComponent.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/Battle/Battle5v5Component.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/Battle/BattleBaseCtrl.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/Battle/BattleBullet.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/Battle/BattleBullet/BattleBulletBezier.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/Battle/BattleBullet/BattleBulletEmpty.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/Battle/BattleBullet/BattleBulletLaser.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/Battle/BattleBullet/BattleBulletLightningBolt.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/Battle/BattleBullet/BattleBulletLightningBolt/LightningBoltScript.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/Battle/BattleBullet/BattleBulletLightningBoltLink.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/Battle/BattleBullet/BattleBulletNormal.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/Battle/BattleBullet/BattleBulletParabolic.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/Battle/BattleBullet/BattleBulletParticle.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/Battle/BattleBulletCtrl.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/Battle/BattleDefine.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/Battle/BattleEffect.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/Battle/BattleEffectCtrl.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/Battle/BattleFiled.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/Battle/BattleHero.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/Battle/BattleRecordCtrl.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/Battle/BattleTeamCtrl.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/Battle/BattleUnitBase.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/Battle/HeroBattleBuff.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/Battle/HeroBattleBuffCtrl.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/Battle/HeroBattleSKill.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/Battle/HeroBattleSkillCtrl.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/Battle/HeroSKill/HeroSkillActionAnimation.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/Battle/HeroSKill/HeroSkillActionBase.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/Battle/HeroSKill/HeroSkillActionBullet.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/Battle/HeroSKill/HeroSkillActionParticle.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/Battle/Other/BattleLookAt.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/Battle/Other/BattleSettings.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/Battle/Other/BattleSlotHandler.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/Battle/Other/BattleUtils.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/Battle/Other/ColoredBgPanel.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/Battle/Other/ILookAtAble.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/Battle/SkillEditor/BulletEditorInspector.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/Battle/SkillEditor/SkillEditorActionAnimation.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/Battle/SkillEditor/SkillEditorActionBase.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/Battle/SkillEditor/SkillEditorActionBullet.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/Battle/SkillEditor/SkillEditorActionParticle.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/Battle/SkillEditor/SkillEditorPreview.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/Battle/SkillTargetData.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/Camera/CameraAdditionalComponent.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/Camera/CameraComponent.Battle5v5Camera.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/Camera/CameraComponent.CityCamera.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/Camera/CameraComponent.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/Camera/CameraComponent.UICamera.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/Camera/CameraComponent.WorldMapCamera.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/CityMap/CityMapComponent.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/CityMap/CityMapComponent/MainCityAreaGenerator.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/CityMap/CityMapComponent/MainCityFog.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/CityMap/MapOperationController.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/CityMap/OpModeBase.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/CityMap/OpModeBuildingConstruct.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/CityMap/OpModeBuildingMove.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/CityMap/OpModeDefault.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/CityMap/OpModeWorldMapDefault.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/CityMap/OpModeWorldMapTownMove.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/CommonEffect/CommonEffect.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/CommonEffect/CommonEffectComponent.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/HUD/HUDComponent.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/HUD/HUDItem.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/HUD/HUDItemObject.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/HUD/HUDItemTickAble.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/Input/CameraInputScheme.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/Input/CameraRig.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/Input/CameraRig3.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/Input/Editor/CameraRigEditor.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/Input/Editor/CameraRigEditor3.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/Input/InputComponent.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/Input/InputScheme.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/Input/InputSchemeSwitcher.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/Input/KeyboardComponent.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/Input/KeyboardMouseInput.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/Input/MouseButtonInfo.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/Input/MouseCursorInfo.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/Input/PinchInfo.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/Input/PointerActionInfo.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/Input/PointerInfo.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/Input/TouchInfo.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/Input/TouchInput.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/Input/WheelInfo.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/WorldMap/AreaBorderDataHolder.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/WorldMap/WorldMapCameraHelper.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/WorldMap/WorldMapComponent.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/WorldMap/WorldMapComponentCtrls/IWMCmp.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/WorldMap/WorldMapComponentCtrls/WMCmpAreaBorderDrawer.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/WorldMap/WorldMapComponentCtrls/WMCmpBuildableGroundDrawer.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/WorldMap/WorldMapComponentCtrls/WMCmpCityDrawer.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/WorldMap/WorldMapComponentCtrls/WMCmpFortsDrawer.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/WorldMap/WorldMapComponentCtrls/WMCmpGroundDrawer.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/WorldMap/WorldMapComponentCtrls/WMCmpLogicCameraCtrl.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/WorldMap/WorldMapComponentCtrls/WMCmpMineDrawer.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/WorldMap/WorldMapComponentCtrls/WMCmpMonsterDrawer.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/WorldMap/WorldMapComponentCtrls/WMCmpMyTownDrawer.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/WorldMap/WorldMapComponentCtrls/WMCmpPreviewDrawer.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/WorldMap/WorldMapComponentCtrls/WMCmpTownDrawer.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/WorldMap/WorldMapComponentCtrls/WMCmpTreeDrawer.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/WorldMap/WorldMapComponentCtrls/WMCmpTroopDrawer.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/WorldMap/WorldMapComponentCtrls/WMCmpUnBuildableGroundDrawer.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/WorldMap/WorldMapOperationCtrl.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/WorldMap/WorldMapStaticData.cs"
"Assets/Scripts/HotUpdate/GameFramework/Component/WorldMap/WorldMapTreeLOD.cs"
"Assets/Scripts/HotUpdate/GameFramework/Data/DataBase.cs"
"Assets/Scripts/HotUpdate/GameFramework/Data/DataSetting.cs"
"Assets/Scripts/HotUpdate/GameFramework/Debugger/ChangeLanguageDebuggerWindow.cs"
"Assets/Scripts/HotUpdate/GameFramework/Definition/Constant/Constant.AssetPriority.cs"
"Assets/Scripts/HotUpdate/GameFramework/Definition/Constant/Constant.Layer.cs"
"Assets/Scripts/HotUpdate/GameFramework/Definition/Constant/Constant.Setting.cs"
"Assets/Scripts/HotUpdate/GameFramework/Entity/Entity.cs"
"Assets/Scripts/HotUpdate/GameFramework/Entity/EntityData.cs"
"Assets/Scripts/HotUpdate/GameFramework/Entity/EntityExtension.cs"
"Assets/Scripts/HotUpdate/GameFramework/Entity/EntityLogic/TestEntity.cs"
"Assets/Scripts/HotUpdate/GameFramework/Enum/EnumEntity.cs"
"Assets/Scripts/HotUpdate/GameFramework/Enum/EnumHUD.cs"
"Assets/Scripts/HotUpdate/GameFramework/Enum/EnumItem.cs"
"Assets/Scripts/HotUpdate/GameFramework/Enum/EnumRed.cs"
"Assets/Scripts/HotUpdate/GameFramework/Enum/EnumSound.cs"
"Assets/Scripts/HotUpdate/GameFramework/Enum/EnumUIForm.cs"
"Assets/Scripts/HotUpdate/GameFramework/Event/BagChangeEventArgs.cs"
"Assets/Scripts/HotUpdate/GameFramework/Event/BagChangeEventArgs1.cs"
"Assets/Scripts/HotUpdate/GameFramework/Event/BeforeChangeSceneEventArgs.cs"
"Assets/Scripts/HotUpdate/GameFramework/Event/ChangeSceneEventArgs.cs"
"Assets/Scripts/HotUpdate/GameFramework/Event/ChaoZhiDotEventArgs.cs"
"Assets/Scripts/HotUpdate/GameFramework/Event/EquipmentChangeEventArgs.cs"
"Assets/Scripts/HotUpdate/GameFramework/Event/EquipmentSwitchEventArgs.cs"
"Assets/Scripts/HotUpdate/GameFramework/Event/HeroChangeEventArgs.cs"
"Assets/Scripts/HotUpdate/GameFramework/Event/ItemChangeEventArgs.cs"
"Assets/Scripts/HotUpdate/GameFramework/Event/MallChangeEventArgs.cs"
"Assets/Scripts/HotUpdate/GameFramework/Event/NoviceCompRefreshEventArgs.cs"
"Assets/Scripts/HotUpdate/GameFramework/Event/On5V5BattleBackEventArgs.cs"
"Assets/Scripts/HotUpdate/GameFramework/Event/OnAreaStateChangeEventArgs.cs"
"Assets/Scripts/HotUpdate/GameFramework/Event/OnBuildingLevelChangeEventArgs.cs"
"Assets/Scripts/HotUpdate/GameFramework/Event/OnBuildingQueueAddEventArgs.cs"
"Assets/Scripts/HotUpdate/GameFramework/Event/OnBuildingQueueRemoveEventArgs.cs"
"Assets/Scripts/HotUpdate/GameFramework/Event/OnBuildLongPressToMoveArgs.cs"
"Assets/Scripts/HotUpdate/GameFramework/Event/OnCityMapTapEventArgs.cs"
"Assets/Scripts/HotUpdate/GameFramework/Event/OnNewBuildingCreateEventArgs.cs"
"Assets/Scripts/HotUpdate/GameFramework/Event/OnOpModeChangeEventArgs.cs"
"Assets/Scripts/HotUpdate/GameFramework/Event/OnPvePathTriggeredEventArgs.cs"
"Assets/Scripts/HotUpdate/GameFramework/Event/OnSceneAwakeEventArgs.cs"
"Assets/Scripts/HotUpdate/GameFramework/Event/OnSceneDestroyEventArgs.cs"
"Assets/Scripts/HotUpdate/GameFramework/Event/OnSceneStartEventArgs.cs"
"Assets/Scripts/HotUpdate/GameFramework/Event/OnWorldMapCameraMoveArgs.cs"
"Assets/Scripts/HotUpdate/GameFramework/Event/OnWorldMapCameraZoomArgs.cs"
"Assets/Scripts/HotUpdate/GameFramework/Event/OnWorldMapChangeLODArgs.cs"
"Assets/Scripts/HotUpdate/GameFramework/Event/OnWorldMapGridDataDirtyArgs.cs"
"Assets/Scripts/HotUpdate/GameFramework/Event/OnWorldMapTapEventArgs.cs"
"Assets/Scripts/HotUpdate/GameFramework/Event/PaymentFinishEventArgs.cs"
"Assets/Scripts/HotUpdate/GameFramework/Event/PeakRankRefreshEventArgs.cs"
"Assets/Scripts/HotUpdate/GameFramework/Event/RedPointEventArgs.cs"
"Assets/Scripts/HotUpdate/GameFramework/Event/TaskChangeEventArgs.cs"
"Assets/Scripts/HotUpdate/GameFramework/Event/TeamChangeEventArgs.cs"
"Assets/Scripts/HotUpdate/GameFramework/Event/TechChangeEventArgs.cs"
"Assets/Scripts/HotUpdate/GameFramework/Event/TrainAnimationEventArgs.cs"
"Assets/Scripts/HotUpdate/GameFramework/Event/TrainContractEventArgs.cs"
"Assets/Scripts/HotUpdate/GameFramework/Event/TrainPassengerEventArgs.cs"
"Assets/Scripts/HotUpdate/GameFramework/Event/UnionChangeEventArgs.cs"
"Assets/Scripts/HotUpdate/GameFramework/Event/UnionHelpChangeEventArgs.cs"
"Assets/Scripts/HotUpdate/GameFramework/Event/UnionJoinChangeEventArgs.cs"
"Assets/Scripts/HotUpdate/GameFramework/Event/UnionTechChangeEventArgs.cs"
"Assets/Scripts/HotUpdate/GameFramework/Event/VipChangeEventArgs.cs"
"Assets/Scripts/HotUpdate/GameFramework/Extension/CameraExtension.cs"
"Assets/Scripts/HotUpdate/GameFramework/Extension/LookAtCamera.cs"
"Assets/Scripts/HotUpdate/GameFramework/Game/GameBase.cs"
"Assets/Scripts/HotUpdate/GameFramework/Game/GameMode.cs"
"Assets/Scripts/HotUpdate/GameFramework/Game/SurvivalGame.cs"
"Assets/Scripts/HotUpdate/GameFramework/HTTP/BaseHttpResp.cs"
"Assets/Scripts/HotUpdate/GameFramework/HTTP/HTTPComponent.cs"
"Assets/Scripts/HotUpdate/GameFramework/HTTP/PayHttpReq.cs"
"Assets/Scripts/HotUpdate/GameFramework/HTTP/PayHttpResp.cs"
"Assets/Scripts/HotUpdate/GameFramework/HTTP/PingHttpResp.cs"
"Assets/Scripts/HotUpdate/GameFramework/HTTP/SigninHttpReq.cs"
"Assets/Scripts/HotUpdate/GameFramework/HTTP/SigninHttpResp.cs"
"Assets/Scripts/HotUpdate/GameFramework/LDLNet/LDLNetComponent.cs"
"Assets/Scripts/HotUpdate/GameFramework/LDLNet/LDLNetDriveComponent.cs"
"Assets/Scripts/HotUpdate/GameFramework/LDLNet/NetEventDispatch.cs"
"Assets/Scripts/HotUpdate/GameFramework/LDLNet/Tcp/NetworkEvent/Message/MessageStream.cs"
"Assets/Scripts/HotUpdate/GameFramework/LDLNet/Tcp/NetworkEvent/Message/MessageStreamException.cs"
"Assets/Scripts/HotUpdate/GameFramework/LDLNet/Tcp/NetworkEvent/NetworkActiveEvent.cs"
"Assets/Scripts/HotUpdate/GameFramework/LDLNet/Tcp/NetworkEvent/NetworkBaseEvent.cs"
"Assets/Scripts/HotUpdate/GameFramework/LDLNet/Tcp/NetworkEvent/NetworkEvent_ConnectFail.cs"
"Assets/Scripts/HotUpdate/GameFramework/LDLNet/Tcp/NetworkEvent/NetworkEvent_ConnectionError.cs"
"Assets/Scripts/HotUpdate/GameFramework/LDLNet/Tcp/NetworkEvent/NetworkEvent_ConnectionLost.cs"
"Assets/Scripts/HotUpdate/GameFramework/LDLNet/Tcp/NetworkEvent/NetworkEvent_ConnectOK.cs"
"Assets/Scripts/HotUpdate/GameFramework/LDLNet/Tcp/NetworkEvent/NetworkEvent_Disconnect.cs"
"Assets/Scripts/HotUpdate/GameFramework/LDLNet/Tcp/NetworkEvent/NetworkEvent_ReceivedMessage.cs"
"Assets/Scripts/HotUpdate/GameFramework/LDLNet/Tcp/NetworkEvent/NetworkEvent_SendMessage.cs"
"Assets/Scripts/HotUpdate/GameFramework/LDLNet/Tcp/NetworkEvent/NetworkEvent_SocketClosed.cs"
"Assets/Scripts/HotUpdate/GameFramework/LDLNet/Tcp/NetworkEvent/NetworkEvent_StartConnect.cs"
"Assets/Scripts/HotUpdate/GameFramework/LDLNet/Tcp/NetworkEvent/NetworkEvent_StartConnectSLG.cs"
"Assets/Scripts/HotUpdate/GameFramework/LDLNet/Tcp/NetworkEvent/NetworkPassiveEvent.cs"
"Assets/Scripts/HotUpdate/GameFramework/LDLNet/Tcp/NetworkEventMgr.cs"
"Assets/Scripts/HotUpdate/GameFramework/LDLNet/Tcp/TCPConnect.cs"
"Assets/Scripts/HotUpdate/GameFramework/LDLNet/Tcp/TCPConnectMgr.cs"
"Assets/Scripts/HotUpdate/GameFramework/LDLNet/Tcp/TCPConnectSLG.cs"
"Assets/Scripts/HotUpdate/GameFramework/LogicData/LogicDataComponent.cs"
"Assets/Scripts/HotUpdate/GameFramework/Procedure/Procedure5v5Battle.cs"
"Assets/Scripts/HotUpdate/GameFramework/Procedure/ProcedureBase.cs"
"Assets/Scripts/HotUpdate/GameFramework/Procedure/ProcedureChangeScene.cs"
"Assets/Scripts/HotUpdate/GameFramework/Procedure/ProcedureHotfixLaunch.cs"
"Assets/Scripts/HotUpdate/GameFramework/Procedure/ProcedureLogin.cs"
"Assets/Scripts/HotUpdate/GameFramework/Procedure/ProcedureMain.cs"
"Assets/Scripts/HotUpdate/GameFramework/Procedure/ProcedureMenu.cs"
"Assets/Scripts/HotUpdate/GameFramework/Procedure/ProcedureNetConnect.cs"
"Assets/Scripts/HotUpdate/GameFramework/Procedure/ProcedurePreload.cs"
"Assets/Scripts/HotUpdate/GameFramework/Procedure/ProcedureWorldMap.cs"
"Assets/Scripts/HotUpdate/GameFramework/Sound/SoundExtension.cs"
"Assets/Scripts/HotUpdate/GameFramework/Time/TimeComponent.cs"
"Assets/Scripts/HotUpdate/GameFramework/UI/CommonButton.cs"
"Assets/Scripts/HotUpdate/GameFramework/UI/UGuiForm.cs"
"Assets/Scripts/HotUpdate/GameFramework/UI/UIComponent/LocalizeText.cs"
"Assets/Scripts/HotUpdate/GameFramework/UI/UIComponent/SwitchPage/MultiLanguage.cs"
"Assets/Scripts/HotUpdate/GameFramework/UI/UIComponent/SwitchPage/SwitchPanelLogic.cs"
"Assets/Scripts/HotUpdate/GameFramework/UI/UIComponent/SwitchPage/UISwitchPage.cs"
"Assets/Scripts/HotUpdate/GameFramework/UI/UIComponent/SwitchPage/UISwitchTag.cs"
"Assets/Scripts/HotUpdate/GameFramework/UI/UIComponent/SwitchPage/UISwitchTagGroup.cs"
"Assets/Scripts/HotUpdate/GameFramework/UI/UIExtension.cs"
"Assets/Scripts/HotUpdate/GameFramework/UI/UIGuiFormEx.cs"
"Assets/Scripts/HotUpdate/GameFramework/Utility/AssetUtility.cs"
"Assets/Scripts/HotUpdate/GameFramework/Utility/UIUtility.cs"
"Assets/Scripts/HotUpdate/Tool/ColorLog.cs"
"Assets/Scripts/HotUpdate/Tool/GMCommandManager.cs"
"Assets/Scripts/HotUpdate/Tool/ToolScriptExtend.cs"
-langversion:9.0
/deterministic
/optimize-
/debug:portable
/nologo
/RuntimeMetadataVersion:v4.0.30319
/nowarn:0169
/nowarn:0649
/nowarn:0282
/nowarn:1701
/nowarn:1702
/utf8output
/preferreduilang:en-US
/additionalfile:"Library/Bee/artifacts/1300b0aEDbg.dag/Game.Hotfix.UnityAdditionalFile.txt"