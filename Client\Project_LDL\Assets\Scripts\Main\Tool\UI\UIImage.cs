﻿using UnityEngine;
using UnityEngine.UI;

public class UIImage: Image
{
    #region 属性

    /// <summary>
    /// 获取和设置组件宽度
    /// </summary>
    /// <value>宽度</value>
    public float Width
    {
        get { return rectTransform.sizeDelta.x; }
        set
        {
            Vector2 sizeDelta = rectTransform.sizeDelta;
            sizeDelta.x = value;
            rectTransform.sizeDelta = sizeDelta;
        }
    }

    /// <summary>
    /// 获取和设置组件高度
    /// </summary>
    /// <value>高度</value>
    public float Height
    {
        get { return rectTransform.sizeDelta.y; }
        set
        {
            Vector2 sizeDelta = rectTransform.sizeDelta;
            sizeDelta.y = value;
            rectTransform.sizeDelta = sizeDelta;
        }
    }

    public Sprite Sprite
    {
        get
        {
            return sprite;
        }
        set
        {
            sprite = value;
            Alpha = 1f;
        }
    }

    public float Alpha
    {
        get
        {
            return color.a;
        }
        set
        {
            Color c = color;
            c.a = value;
            color = c;
        }
    }

    private CanvasGroup _canvasGroup;

    public CanvasGroup CanvasGroup
    {
        get
        {
            if (_canvasGroup == null)
            {
                _canvasGroup = this.CreateCanvasGroup();
            }

            return _canvasGroup;
        }
    }

    #endregion

    #region 生命周期

    protected override void Awake()
    {
        base.Awake();
        if (type == Type.Tiled)
        {
            //Debug.LogError("!! UIIMAGE 禁止使用 tiled 填充:" + transform.root.name);
        }

        // if (Sprite == null)
        // {
        //     Alpha = 0;
        // }

        if (Alpha <= 0)
        {
            type = Type.Simple;
        }
    }

    #endregion

    public void SetActiveByCanvasGroup(bool b)
    {
        CanvasGroup.SetActiveByCanvasGroup(b);
    }

    /// <summary>
    /// 设置图片置灰
    /// </summary>
    /// <param name="isGrey">是否置灰</param>
    public void SetImageGray(bool isGrey, bool isBlack = false, float blackFactor = 1)
    {
        var shader = isGrey ? Shader.Find("UIShader/UIMaterialGrey") : Shader.Find("UI/Default");
        var material = new Material(shader);
        if (isGrey && isBlack)
        {
            material.SetFloat("_IsBlack", blackFactor);
        }
        this.material = material;
    }
}