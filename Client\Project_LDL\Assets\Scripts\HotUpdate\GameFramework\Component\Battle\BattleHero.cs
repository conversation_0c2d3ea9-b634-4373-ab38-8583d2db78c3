using System;
using System.Collections.Generic;
using Battle;
using DG.Tweening;
using Game.Hotfix.Config;
using GameFramework;
using GameFramework.Event;
using JetBrains.Annotations;
using UnityEngine;
using UnityGameFramework.Runtime;
using attributes_type = PbGameconfig.attributes_type;
using itemid = PbGameconfig.itemid;

namespace Game.Hotfix
{
    public enum EnumBattleHeroAnimation
    {
        IDLE,
        RUN,
        SKILL1,
        SKILL2,
        DIE
    }

    public class BattleHero : BattleUnitBase
    {
        public HeroBattleSkillCtrl HeroBattleSkillCtrl => m_HeroBattleSkillCtrl;
        public HeroBattleBuffCtrl HeroBattleBuffCtrl => m_HeroBattleBuffCtrl;
        
        public EnumBattlePos TeamUid => m_TeamUid;
        public EnumBattlePos BattlePos => m_TeamUid;
        /// <summary>
        /// 相对位置 永远是 1-5
        /// </summary>
        public EnumBattlePos RelativeBattlePos
        {
            get
            {
                if ((int)BattlePos > 10)
                    return (EnumBattlePos)((int)BattlePos - 10);
                return BattlePos;
            }
        }

        public BattleFiled BattleFiled => m_BattleFiled;
        public uint Level => m_TeamHero?.Level ?? 0;
        public uint Star =>  m_TeamHero?.StarStage ?? 0;
        public ulong Power =>  m_TeamHero?.Power ?? 0;

        public bool IsDie => m_IsDie;
        public int HeroId => m_HeroId;

        private BattleFiled m_BattleFiled;
        private EnumBattlePos m_TeamUid;
        private Transform m_BattlePos;
        private int m_HeroId;

        private EL_BattleHero m_BattleHero;
        private battle_role m_Config;
        private Dictionary<skill_act_group, string> m_AnimationList;

        private bool m_IsDie = false;

        private bool m_IsBattle = false;
        
        private HeroBattleSkillCtrl m_HeroBattleSkillCtrl;
        private HeroBattleBuffCtrl m_HeroBattleBuffCtrl;
        [CanBeNull] private TeamHero m_TeamHero;
        
        private int m_EditorSkillId;
        
        /// <summary>
        /// m_Hp/m_HpMax....
        /// </summary>
        private Dictionary<ChangeType, long> m_Attr;

        public void Init(EnumBattlePos teamUid, Transform battlePos, int heroId, BattleFiled battleFiled,
            Action<Entity> onLoaded = null,TeamHero teamHero = null,bool isBattle = false)
        {
            m_Config = Game.GameEntry.LDLTable.GetTableById<battle_role>(heroId);

            base.Init(onLoaded, battlePos.position, battlePos.rotation, Vector3.one * m_Config.scale);
            m_BattlePos = battlePos;
            m_BattleFiled = battleFiled;
            m_TeamUid = teamUid;
            m_HeroId = heroId;
            m_TeamHero = teamHero;
            m_IsBattle = isBattle;

            m_IsDie = false;
            
            m_HeroBattleSkillCtrl = new HeroBattleSkillCtrl(this);
            m_HeroBattleSkillCtrl.Init();

            m_HeroBattleBuffCtrl = new HeroBattleBuffCtrl(m_BattleFiled, this);
            m_HeroBattleBuffCtrl.Init();

            m_Attr = new Dictionary<ChangeType, long>
            {
                { ChangeType.AttrHp, 0 },
                { ChangeType.AttrMaxHp, 0 }
            };

            if (teamHero != null)
            {
                //初始化attr
                foreach (var attr in teamHero.Attrs)
                {
                    if (attr.Type == attributes_type.Hp)
                    {
                        SetAttr(ChangeType.AttrHp, attr.Value);
                        SetAttr(ChangeType.AttrMaxHp, attr.Value);
                        break;
                    }
                }
                //初始化技能
                foreach (var skillData in teamHero.Skills)
                {
                    skill_config skillConfig = GameEntry.LDLTable.GetTableById<skill_config>(skillData.Id);
                    if (skillConfig != null)
                    {
                        skill_show skillShowCfg = GameEntry.LDLTable.GetTableById<skill_show>(skillConfig.show_id);
                        if (skillShowCfg != null)
                        {
                            HeroBattleSKill skill = new HeroBattleSKill(m_BattleFiled, skillShowCfg, this, skillConfig);
                            m_HeroBattleSkillCtrl.AddSkill(skill);
                        }
                    }
                }
            }
            
            
            
            m_AnimationList = InitAnimationList(m_Config);

            Load();
        }

        protected override Type GetEntityLogicType()
        {
            return typeof(EL_BattleHero);
        }

        protected override string GetPrefabPath()
        {
            return m_Config.res_location;
        }

        protected override ED_BattleUnitBase GetBattleUnitData()
        {
            ED_BattleHero data =
                new ED_BattleHero(Game.GameEntry.Entity.GenerateSerialId());
            data.Position = Position;
            data.Rotation = Rotation;
            data.Scale = Scale;

            return data;
        }

        protected override void OnTick(float dt)
        {
            m_HeroBattleSkillCtrl.OnTick(dt);
        }

        protected override void UnInit()
        {
            m_HeroBattleSkillCtrl.UnInit();
        }

        protected override void OnLoaded(Entity entity)
        {
            if (entity is EL_BattleHero hero)
            {
                m_BattleHero = hero;
                #if UNITY_EDITOR
                if (m_BattleFiled.IsDebug)
                {
                    var skillEditorPreview = entity.gameObject.GetOrAddComponent<SkillEditorPreview>();
                    if(m_EditorSkillId>0)
                    {
                        skillEditorPreview.opSkillId = m_EditorSkillId;
                        skillEditorPreview.ImportData();
                    }
                }
                #endif
                
                if(m_IsBattle)
                {
                    PlayAnim(skill_act_group.run);

                    var offset = new Vector3(0, 0, BattleDefine.BATTLE_MOVE_IN_OFFSET);
                    var battlePos = m_BattleFiled.GetTeamPosByUid(BattlePos);
                    var fromPos = GetSide() == EnumBattleSide.Left? battlePos - offset : battlePos + offset;
                    
                    DOTween.To(() => 0, curValue =>
                    {
                        SetPosition(Vector3.Lerp(fromPos, battlePos, curValue));
                    }, 1f, BattleDefine.BATTLE_WAIT_TIME).OnComplete(() =>
                    {
                        SetPosition(battlePos);
                        PlayAnim(skill_act_group.idle);
                    });
                }
            }
        }

        private Dictionary<skill_act_group, string> InitAnimationList(battle_role config)
        {
            Dictionary<skill_act_group, string> list = new Dictionary<skill_act_group, string>();
            list[skill_act_group.idle] = config.idle;
            list[skill_act_group.run] = config.run;
            list[skill_act_group.skill_1] = config.skill_1;
            list[skill_act_group.skill_2] = config.skill_2;
            list[skill_act_group.die] = config.die;
            return list;
        }

        public void PlayAnim(skill_act_group animation, Vector3? targetPos = null)
        {
            if (m_AnimationList.ContainsKey(animation) && m_BattleHero != null)
            {
                m_BattleHero.PlayAnimation(m_AnimationList[animation], targetPos);
            }
        }

        /// <summary>
        /// 死亡
        /// </summary>
        public void Die()
        {
            if (IsDie) return;

            m_IsDie = true;
            
            PlayAnim(skill_act_group.die);
            
            string timeKey = "BattleHeroDie"+BattlePos;
            Timers.Instance.Remove(timeKey);
            Timers.Instance.Add(timeKey,1f, (param) =>
            {
                if (m_Config.die_effect > 0)
                {
                    var dieEffect = m_BattleFiled.EffectCtrl.GetEffect(m_Config.die_effect);
                    dieEffect.SetPosition(Position);
                }
                m_BattleHero.Hide(true);
                m_BattleFiled.SendEvent(BattleFiledEvent.OnHeroDie, BattlePos);

                m_HeroBattleBuffCtrl.RemoveAllEffect();
            });
            
        }

        /// <summary>
        /// 复活
        /// </summary>
        public void Resurgence()
        {
            
        }

        public void DoFlash()
        {
            m_BattleHero?.DoFlash(true);
        }
        
        public Transform GetSlot(slot slot)
        {
            if (m_BattleHero != null)
            {
                return m_BattleHero.GetSlot(slot);
            }

            return null;
        }

        public Vector3 GetSlotPosition(slot slot)
        {
            if (m_BattleHero != null)
            {
                return m_BattleHero.GetSlotPosition(slot);
            }

            return Vector3.zero;
        }

        public Quaternion GetSlotRotation(slot slot)
        {
            if (m_BattleHero != null)
            {
                return m_BattleHero.GetSlotRotation(slot);
            }

            return Quaternion.identity;
        }

        public EnumBattleSide GetSide()
        {
            if ((int)TeamUid <= 10)
            {
                return EnumBattleSide.Left;
            }

            return EnumBattleSide.Right;
        }

        public void MoveBack(float duration = 0.3f)
        {
            MoveTo(BattlePos, duration);
        }

        public void MoveTo(EnumBattlePos targetPos, float duration = 0.3f)
        {
            var position = Position;
            var targetWorldPos = m_BattleFiled.GetChoosePos(targetPos);

            DOTween.To(() => position, newPos => { SetPosition(newPos); }, targetWorldPos.position, duration);
        }

        #if UNITY_EDITOR
        public void LoadEditorSkill(int skillId)
        {
            m_EditorSkillId = skillId;
        }
        #endif
        
        #region 属性

        public void GetHPState(out long curHP,out long maxHP)
        {
            curHP = GetAttr(ChangeType.AttrHp) ?? 0;
            maxHP = GetAttr(ChangeType.AttrMaxHp) ?? 0;
        }
        
        public void AddAttr(int mask,ChangeType changeType,long addValue)
        {
            var v = GetAttr(changeType);
            if (v != null)
            {
                SetAttr(changeType, v.Value + addValue);
                
                var param = ReferencePool.Acquire<BattleFiledOnHeroAttrChangeParam>();
                param.ChangeType = changeType;
                param.Value = addValue;
                param.BattlePos = BattlePos;
                param.Mask = mask;
                m_BattleFiled.SendEvent(BattleFiledEvent.OnHeroAttrChange, param);
            }
        }

        public long? GetAttr(ChangeType type)
        {
            if (m_Attr.TryGetValue(type, out long value))
            {
                return value;
            }
            Debug.LogError("Attributes should not appear here(get):"+type);
            return null;
        }

        public void SetAttr(ChangeType type,long newValue)
        {
            if (m_Attr.TryGetValue(type, out long value))
            {
                m_Attr[type] = newValue;
            }
            else
            {
                Debug.LogError("Attributes should not appear here(set):"+type);
            }
        }

        #endregion
    }
}