%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 7_ZSJ
  m_Shader: {fileID: 4800000, guid: 767e660b49a03bc4484fe0b9ebda1d02, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords:
  - _RIMLIGHTING_ON
  m_InvalidKeywords: []
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 1
  m_CustomRenderQueue: -1
  stringTagMap:
    RenderType: Opaque
  disabledShaderPasses: []
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BaseMap:
        m_Texture: {fileID: 2800000, guid: ffc2a133dd303e049b23bc022072bf6c, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 2800000, guid: f17833f0c0014734f8f659324cf39c60, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 2800000, guid: bdfa6b1dcd2f33e40a1a986814c5fdeb, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NormalRGMap:
        m_Texture: {fileID: 2800000, guid: 66264faaf60850a4db47ea30e8b1479f, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SpecCube0:
        m_Texture: {fileID: 8900000, guid: 3644c40a08f353f4e8706f10645f9894, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SpecGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AddPrecomputedVelocity: 0
    - _AlphaClip: 0
    - _AlphaToMask: 0
    - _Blend: 0
    - _BlendModePreserveSpecular: 1
    - _BumpScale: 1.14
    - _ClearCoatMask: 0
    - _ClearCoatSmoothness: 0
    - _CubeIntensity: 0.107
    - _Cull: 0
    - _Cutoff: 0.5
    - _DetailAlbedoMapScale: 1
    - _DetailNormalMapScale: 1
    - _DistanceAttenuation: 1
    - _DstBlend: 0
    - _DstBlendAlpha: 0
    - _EnvironmentReflections: 1
    - _GlossMapScale: 1
    - _Glossiness: 0.14142135
    - _GlossyReflections: 1
    - _Metallic: 1
    - _Mode: 0
    - _OcclusionStrength: 0
    - _Parallax: 0.02
    - _QueueControl: 0
    - _QueueOffset: 0
    - _ReceiveShadows: 1
    - _RimDir: 0
    - _RimDown: 0
    - _RimIntensity: 1.02
    - _RimLeft: 0
    - _RimLighting: 1
    - _RimLightingCam: 0
    - _RimOuterThickness: 0.207
    - _RimPower: 14.4
    - _RimRight: 0
    - _RimUp: 0
    - _Smoothness: 1
    - _SmoothnessTextureChannel: 0
    - _SpecularHighlights: 1
    - _SrcBlend: 1
    - _SrcBlendAlpha: 1
    - _Surface: 0
    - _UVSec: 0
    - _WorkflowMode: 1
    - _ZWrite: 1
    m_Colors:
    - _AuxiliaryLightColor: {r: 2.608238, g: 1.829811, b: 0.012303039, a: 1}
    - _AuxiliaryLightDirection: {r: 0.65026265, g: 0.074978665, b: 0.7560002, a: 0}
    - _BaseColor: {r: 0.79073066, g: 0.79073066, b: 0.79073066, a: 1}
    - _Color: {r: 0.79073066, g: 0.79073066, b: 0.79073066, a: 1}
    - _CustomLightColor: {r: 3.562958, g: 3.562958, b: 3.562958, a: 1}
    - _CustomLightDirection: {r: 0.05406631, g: 0.2486897, b: -0.967073, a: 0}
    - _EmissionColor: {r: 1.7411011, g: 1.7411011, b: 1.7411011, a: 1}
    - _RimInnerColor: {r: 0.75, g: 0.586, b: 0.135, a: 1}
    - _RimOuterColor: {r: 0, g: 0, b: 0, a: 1}
    - _RimView: {r: 0, g: 0, b: 0, a: 0}
    - _SpecColor: {r: 0.19999996, g: 0.19999996, b: 0.19999996, a: 1}
  m_BuildTextureStacks: []
  m_AllowLocking: 1
