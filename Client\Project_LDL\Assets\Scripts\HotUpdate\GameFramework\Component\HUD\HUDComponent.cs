using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityGameFramework.Runtime;
using GameFramework.ObjectPool;
using GameFramework.Event;
using GameFramework.Resource;

namespace Game.Hotfix
{
    [DisallowMultipleComponent]
    [AddComponentMenu("GameCustom/HUDComponent")]
    public class HUDComponent : GameFrameworkComponent
    {
        private static uint _serialId = 0;

        public static uint GenerateSerialId()
        {
            return _serialId++;
        }
        
        [SerializeField] private Transform m_HUDInstanceRoot;                // HUD 实例根节点
        [SerializeField] private CanvasGroup m_HUDCanvasGroup;               // HUD 画布组
        [SerializeField] private float m_InstanceAutoReleaseInterval = 60f;  // 对象池实例自动释放间隔
        [SerializeField] private float m_InstanceExpireTime = 60f;           // 对象池实例存活时间
        [SerializeField] private int m_InstancePoolCapacity = 16;            // 对象池初始容量
        private Canvas m_cachedCanvas;                                       // 缓存 Canvas 组件
        private IObjectPool<HUDItemObject> m_HUDItemObjectPool;              // HUDItemObject 对象池
        private List<HUDItem> m_ActiveHUDItems;                              // 激活的 HUDItem 列表
        private Dictionary<uint,HUDItem> m_ActiveHUDItemsDic;                // 激活的 HUDItem 字典
        private List<HUDLoadingInfo> m_HUDLoadingInfos;                      // HUD 加载信息列表

        private void Start()
        {
            if (m_HUDInstanceRoot == null)
            {
                return;
            }

            m_cachedCanvas = m_HUDInstanceRoot.GetComponent<Canvas>();

            m_HUDItemObjectPool = GameEntry.ObjectPool.CreateSingleSpawnObjectPool<HUDItemObject>("HUDItem", m_InstancePoolCapacity);
            m_HUDItemObjectPool.AutoReleaseInterval = m_InstanceAutoReleaseInterval;
            m_HUDItemObjectPool.ExpireTime = m_InstanceExpireTime;

            m_ActiveHUDItems = new List<HUDItem>();
            m_ActiveHUDItemsDic = new Dictionary<uint, HUDItem>();
            m_HUDLoadingInfos = new List<HUDLoadingInfo>();

            GameEntry.Event.Subscribe(ChangeSceneEventArgs.EventId, OnLoadSceneSuccess);
        }

        /// <summary>
        /// 场景加载成功
        /// </summary>
        /// <param name="sender">发送者</param>
        /// <param name="e">参数</param>
        private void OnLoadSceneSuccess(object sender, GameEventArgs e)
        {
            ChangeSceneEventArgs a = (ChangeSceneEventArgs)e;
            SetCamera(GameEntry.Camera.CurUseCamera);
        }

        /// <summary>
        /// 设置 HUD 画布的相机
        /// </summary>
        /// <param name="camera">相机</param>
        public void SetCamera(Camera camera)
        {
            if (m_cachedCanvas == null)
            {
                return;
            }

            m_cachedCanvas.worldCamera = camera;
        }

        /// <summary>
        /// 显示 HUD
        /// </summary>
        /// <param name="entity">实体</param>
        /// <param name="hudItemTypeName">HUD 类型名称</param>
        /// <param name="isSingleton">是否单例</param>
        public uint ShowHUD(Entity entity, EnumHUD hudItemTypeName, bool isSingleton = false,object param = null)
        {
            if (entity == null)
            {
                Debug.LogWarning("Entity is invalid.");
                return 0;
            }

            // 检查HUD系统是否准备就绪
            if (!IsHUDSystemReady())
            {
                Debug.LogWarning($"HUD system is not ready for creating {hudItemTypeName}");
                return 0;
            }

            HUDItem hudItem = GetActiveHUDItem(entity, hudItemTypeName, isSingleton);

            if (hudItem == null)
            {
                uint uid = CreateHUDItem(entity, hudItemTypeName,param);
                return uid;
            }
            else
            {
                hudItem.Refresh(entity.transform);
                return hudItem.Uid;
            }
        }

        /// <summary>
        /// 检查HUD系统是否准备就绪
        /// </summary>
        private bool IsHUDSystemReady()
        {
            if (m_HUDInstanceRoot == null)
            {
                Debug.LogWarning("HUD instance root is null");
                return false;
            }

            if (m_HUDItemObjectPool == null)
            {
                Debug.LogWarning("HUD item object pool is null");
                return false;
            }

            if (m_ActiveHUDItems == null || m_ActiveHUDItemsDic == null || m_HUDLoadingInfos == null)
            {
                Debug.LogWarning("HUD collections are not initialized");
                return false;
            }

            return true;
        }

        /// <summary>
        /// 隐藏 HUD
        /// </summary>
        /// <param name="hudItem">HUD 实例</param>
        public void HideHUD(HUDItem hudItem)
        {
            hudItem.UnInit();
            
            m_ActiveHUDItems.Remove(hudItem);
            m_ActiveHUDItemsDic.Remove(hudItem.Uid);
            
            m_HUDItemObjectPool.Unspawn(hudItem);
        }

        public void HideHUD(uint uid)
        {
            if (m_ActiveHUDItemsDic.TryGetValue(uid,out var item))
            {
                HideHUD(item);
            }
            else if(HasLoadingInfo(uid))
            {
                RemoveLoadingInfo(uid);
            }
        }
        
        /// <summary>
        /// 移除所有
        /// </summary>
        public void HideAll()
        {
            m_ActiveHUDItemsDic.Clear();
            for (int i = m_ActiveHUDItems.Count-1; i >=0; i--)
            {
                HideHUD(m_ActiveHUDItems[i]);
            }
        }

        /// <summary>
        /// 获取激活的 HUD 实例
        /// </summary>
        /// <param name="entity">实体</param>
        /// <param name="hudItemTypeName">HUD 类型名称</param>
        /// <param name="isSingleton">是否单例</param>
        /// <returns>HUD 实例</returns>
        private HUDItem GetActiveHUDItem(Entity entity, EnumHUD hudItemTypeName, bool isSingleton)
        {
            if (entity == null)
            {
                return null;
            }
        
            for (int i = 0; i < m_ActiveHUDItems.Count; i++)
            {
                // 单例
                if (isSingleton)
                {
                    if (m_ActiveHUDItems[i].HUDItemTypeName == hudItemTypeName)
                    {
                        return m_ActiveHUDItems[i];
                    }
                }
                // 多实例
                else
                {
                    if (m_ActiveHUDItems[i].Owner == entity
                    && m_ActiveHUDItems[i].HUDItemTypeName == hudItemTypeName)
                    {
                        return m_ActiveHUDItems[i];
                    }
                }
            }
        
            return null;
        }

        /// <summary>
        /// 创建 HUD 实例
        /// </summary>
        /// <param name="entity">实体</param>
        /// <param name="hudItemTypeName">HUD 类型名称</param>
        private uint CreateHUDItem(Entity entity, EnumHUD hudItemTypeName,object param)
        {
            string hudItemTypeNameStr = Enum.GetName(typeof(EnumHUD), hudItemTypeName);
            string assetName = AssetUtility.GetHUDAsset(hudItemTypeNameStr);
            
            HUDItem hudItem = null;
            HUDItemObject hudItemObject = m_HUDItemObjectPool.Spawn(assetName);

            uint uid = GenerateSerialId();
            
            if (hudItemObject != null)
            {
                hudItem = (HUDItem)hudItemObject.Target;

                if (hudItem == null)
                {
                    Debug.LogError("HUD item is invalid.");
                    return 0;
                }
                hudItem.HUDItemTypeName = hudItemTypeName;
                hudItem.Init(entity, param, uid);
                m_ActiveHUDItems.Add(hudItem);
                m_ActiveHUDItemsDic.Add(hudItem.Uid, hudItem);
                return hudItem.Uid;
            }
            else
            {
                // 正在加载实体的 HUD，不重复加载
                if (HasLoadingInfo(entity, hudItemTypeName))
                {
                    Debug.LogWarningFormat("HUD item '{0}' '{1}' is loading.", entity.name, hudItemTypeName);
                    return 0;
                }

                // 记录正在加载的 HUD 信息
                m_HUDLoadingInfos.Add(new HUDLoadingInfo()
                {
                    hudItemTypeName = hudItemTypeName,
                    entity = entity,
                    param = param,
                    uid = uid,
                });

                // 开始加载资源
                GameEntry.Resource.LoadAsset(assetName, Constant.AssetPriority.HUDAsset, new LoadAssetCallbacks(
                (assetName, asset, duration, userData) =>
                {
                    GameObject go = asset as GameObject;
                    if (go == null)
                    {
                        Debug.LogErrorFormat("Loaded asset '{0}' is invalid.", assetName);
                        return;
                    }

                    if (!HasLoadingInfo(uid))
                    {
                        Debug.LogErrorFormat("LoadingInfo has removed '{0}'.", assetName);
                        return;
                    }

                    hudItem = Instantiate(go).GetComponent<HUDItem>();

                    if (hudItem == null)
                    {
                        Debug.LogErrorFormat("Can not create HUD item '{0}'.", hudItemTypeName);
                        return;
                    }
                    
                    Transform transform = hudItem.transform;
                    transform.SetParent(m_HUDInstanceRoot);
                    m_HUDItemObjectPool.Register(HUDItemObject.Create(hudItem,assetName), true);
                
                    hudItem.HUDItemTypeName = hudItemTypeName;
                    hudItem.Init(entity, param, uid);
                    m_ActiveHUDItems.Add(hudItem);
                    m_ActiveHUDItemsDic.Add(hudItem.Uid, hudItem);

                    RemoveLoadingInfo(entity, hudItemTypeName);
                },

                (assetName, status, errorMessage, userData) =>
                {
                    Debug.LogErrorFormat("Can not load HUD asset '{0}' with error message '{1}'.", assetName, errorMessage);
                    RemoveLoadingInfo(entity, hudItemTypeName);
                }));
                return uid;
            }
        }

        /// <summary>
        /// 是否有正在加载的 HUD 信息
        /// </summary>
        /// <param name="entity">实体</param>
        /// <param name="hudItemTypeName">HUD 类型名称</param>
        /// <returns>状态</returns>
        bool HasLoadingInfo(Entity entity, EnumHUD hudItemTypeName)
        {
            for (int i = 0; i < m_HUDLoadingInfos.Count; i++)
            {
                if (m_HUDLoadingInfos[i].hudItemTypeName == hudItemTypeName
                && m_HUDLoadingInfos[i].entity == entity)
                {
                    return true;
                }
            }
            return false;
        }

        bool HasLoadingInfo(uint uid)
        {
            return m_HUDLoadingInfos.Any(t => t.uid == uid);
        }
        
        /// <summary>
        /// 移除正在加载的 HUD 信息
        /// </summary>
        /// <param name="entity">实体</param>
        /// <param name="hudItemTypeName">HUD 类型名称</param>
        void RemoveLoadingInfo(Entity entity, EnumHUD hudItemTypeName)
        {
            for (int i = m_HUDLoadingInfos.Count - 1; i >= 0; i--)
            {
                if (m_HUDLoadingInfos[i].hudItemTypeName == hudItemTypeName
                && m_HUDLoadingInfos[i].entity == entity)
                {
                    m_HUDLoadingInfos.RemoveAt(i);
                    break;
                }
            }
        }
        
        void RemoveLoadingInfo(uint uid)
        {
            for (int i = m_HUDLoadingInfos.Count - 1; i >= 0; i--)
            {
                if (m_HUDLoadingInfos[i].uid == uid)
                {
                    m_HUDLoadingInfos.RemoveAt(i);
                    break;
                }
            }
        }

        /// <summary>
        /// 显示 HUD 画布组
        /// </summary>
        public void ShowHUDGroup()
        {
            m_HUDCanvasGroup.alpha = 1f;
            m_HUDCanvasGroup.blocksRaycasts = true;
        }

        /// <summary>
        /// 隐藏 HUD 画布组
        /// </summary>
        public void HideHUDGroup()
        {
            m_HUDCanvasGroup.alpha = 0f;
            m_HUDCanvasGroup.blocksRaycasts = false;
        }

        /// <summary>
        /// HUD 加载信息
        /// </summary>
        class HUDLoadingInfo
        {
            public EnumHUD hudItemTypeName;  // HUD 类型名称
            public Entity entity;           // 实体
            public object param;            //参数
            public uint uid;                //唯一id
        }
    }
}
