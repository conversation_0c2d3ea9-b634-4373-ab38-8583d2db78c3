using System;
using EasingCore;
using Game.Hotfix.Config;
using UnityEngine;

namespace Game.Hotfix
{
    public enum EnumBattleSide
    {
        Left,
        Right
    }


    public enum EnumBattlePos
    {
        PosL1 = 1,
        PosL2 = 2,
        PosL3 = 3,
        PosL4 = 4,
        PosL5 = 5,
        PosR1 = 11,
        PosR2 = 12,
        PosR3 = 13,
        PosR4 = 14,
        PosR5 = 15,
    }
    
    [System.Flags]
    public enum EnumBattleMask
    {
        maskNil                  = 0,
        maskDodge                = 1 << 0, // 闪避(目标) 1
        maskCrit                 = 1 << 1, // 暴击(目标) 2
        maskImmunity             = 1 << 2, // 免疫(目标) 4
        maskBlock                = 1 << 3, // 反击(目标) 8
        maskPhysicalUltimateHurt = 1 << 4, // 物理大招伤害(目标) 16
        maskEnergyUltimateHurt   = 1 << 5, // 能量大招伤害(目标) 32
    }

    public enum EnumBattleRelation
    {
        None,
        Relation3_0,//3个同类型
        Relation3_2,//3个同类型+2个同类型
        Relation4_0,//4个同类型
        Relation5_0,//5个同类型
    }
    
    public static class BATTLE_TIME_EASE
    {
        public static int None = 0;

        public static int InSine = 1;
        public static int OutSine = 2;
        public static int InOutSine = 3;

        public static int InQuad = 4;
        public static int OutQuad = 5;
        public static int InOutQuad = 6;

        public static int InCubic = 7;
        public static int OutCubic = 8;
        public static int InOutCubic = 9;

        public static int InQuart = 10;
        public static int OutQuart = 11;
        public static int InOutQuart = 12;

        public static int Bezier = 13;
    }

    public static class BattleDefine
    {
        /// <summary>
        /// 每秒多少帧
        /// </summary>
        public const int BATTLE_FRAME_SECONDS = 24;

        /// <summary>
        /// 每帧多少秒
        /// </summary>
        public const float BATTLE_FRAME_DURATION = 1f / BATTLE_FRAME_SECONDS;

        /// <summary>
        /// 战斗总时长
        /// </summary>
        // public const int BATTLE_TOTAL_SECONDS_Max = 300;

        /// <summary>
        /// 战斗准备时间
        /// </summary>
        public const float BATTLE_WAIT_TIME = 3f;
        
        /// <summary>
        /// 位置0-10 都是攻击位置
        /// </summary>
        public static int AttackTeamMaxPos = 10;

        /// <summary>
        /// 战斗入场偏移量
        /// </summary>
        public const int BATTLE_MOVE_IN_OFFSET = 1; 
        
        public static Color GetColorByHeroQuality(quality quality)
        {
            switch (quality)
            {
                case quality.quality_nil:
                    return Color.gray;
                case quality.quality_white:
                    return Color.white;
                case quality.quality_green:
                    return Color.green;
                case quality.quality_blue:
                    return Color.blue;
                case quality.quality_purple:
                    return Color.magenta;
                case quality.quality_orange:
                    return new Color(255/255f, 153/255f, 51/255f);
                case quality.quality_red:
                    return Color.red;
                default:
                    return Color.gray;
            }
        }
    }
    
    
}