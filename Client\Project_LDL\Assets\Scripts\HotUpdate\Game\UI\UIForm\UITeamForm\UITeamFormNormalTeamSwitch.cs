using System.Collections.Generic;
using Game.Hotfix.Config;
using UnityEngine;
using UnityEngine.Serialization;
using UnityEngine.UI;

namespace Game.Hotfix
{
    public class UITeamFormNormalTeamSwitch : MonoBehaviour
    {
        public ToggleGroup toggleGroup;
        public List<UIToggle> toggleList;
        public UIToggle curToggle;

        private UITeamForm m_UiTeamForm;
        public void OnOpen(UITeamForm uiTeamForm)
        {   
            m_UiTeamForm = uiTeamForm;
            
            for (int i = 0; i < toggleList.Count; i++)
            {
                var index = i;
                var toggle = toggleList[i];
                Transform imgLock = toggle.transform.Find("imgLock");
                UIImage imgBackground = toggle.transform.Find("Background").GetComponent<UIImage>();
                UIImage imgCheckMark = toggle.transform.Find("Background/CheckMark").GetComponent<UIImage>();
                bool teamIsUnlock = GameEntry.LogicData.BuildingData.GetTeamIsUnlock(index + 1);
                imgLock.gameObject.SetActive(!teamIsUnlock);

                // 检查编队是否被占用（仅对贸易货车防守编队）
                bool isOccupied = false;
                if (m_UiTeamForm != null && m_UiTeamForm.CurTeamFormType == UITeamFormType.TradeTruckDefend && index < m_UiTeamForm.TeamTypes.Count)
                {
                    var teamType = m_UiTeamForm.TeamTypes[index];
                    isOccupied = GameEntry.TradeTruckData.IsOccupiedTradeVanDefendTeams(teamType);
                }
                imgBackground.SetImageGray(isOccupied, true, 0.2f);
                imgCheckMark.SetImageGray(isOccupied, true, 0.2f);

                if (toggle.isOn)
                {
                    curToggle = toggle;
                }
                toggle.onValueChanged.RemoveAllListeners();
                toggle.onValueChanged.AddListener((value) =>
                {
                    if (!teamIsUnlock)
                    {
                        if (curToggle != null)
                        {
                            curToggle.isOn = true;
                        }

                        if (index != toggleList.Count - 1)
                        {
                            GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
                            {
                                Content = ToolScriptExtend.GetLang(1333),
                            });
                        }
                        else
                        {
                            GameEntry.UI.OpenUIForm(EnumUIForm.UIMallForm,paymenttype.paymenttype_monthycard);
                        }

                        return;
                    }

                    // 检查编队是否被占用（仅对贸易货车防守编队）
                    if (value && m_UiTeamForm != null && m_UiTeamForm.CurTeamFormType == UITeamFormType.TradeTruckDefend)
                    {
                        var teamType = m_UiTeamForm.TeamTypes[index];
                        var occupiedTeams = GameEntry.TradeTruckData.GetOccupiedTradeVanDefendTeams();
                        if (occupiedTeams.Contains(teamType))
                        {
                            // 恢复之前的选择
                            if (curToggle != null)
                            {
                                curToggle.isOn = true;
                            }

                            // 显示编队被占用的飘字提示
                            string message = ToolScriptExtend.GetLang(1337);
                            GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
                            {
                                Content = message,
                            });
                            return;
                        }
                    }

                    if (value)
                    {
                        curToggle = toggle;
                        m_UiTeamForm?.SwitchTeamIndex(index);
                    }

                });
            }
        }
        
        public void OnClose()
        {
            
            for (int i = 0; i < toggleList.Count; i++)
            {
                var toggle = toggleList[i];
                toggle.onValueChanged.RemoveAllListeners();
            }
        }

        public void SwitchTo(int index, bool? force = null)
        {
            if (index < toggleList.Count)
            {
                toggleList[index].isOn = true;
            }
        }
    }
}
