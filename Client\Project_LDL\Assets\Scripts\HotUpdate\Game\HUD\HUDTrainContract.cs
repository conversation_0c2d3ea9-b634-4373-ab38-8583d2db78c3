using GameFramework.Event;
using UnityEngine;

namespace Game.Hotfix
{
    public class HUDTrainContract : HUDItemTickAble
    {
        public UIButton m_btnBuy;
        public UIButton m_btnEnter;
        public UIButton m_btnExit;
        public UIText m_txtCount;

        void Start()
        {
            m_btnBuy.onClick.AddListener(OnBtnBuyClick);
            m_btnEnter.onClick.AddListener(OnBtnEnterClick);
            m_btnExit.onClick.AddListener(OnBtnExitClick);
        }

        protected override void OnInit(object param)
        {
            base.OnInit(param);
            GameEntry.Event.Subscribe(TrainContractEventArgs.EventId, OnTrainContractEventArgs);
            GameEntry.Event.Subscribe(UnionJoinChangeEventArgs.EventId, OnUnionJoinChangeEventArgs);

            TrainStatus trainStatus = GameEntry.TradeTruckData.GetTrainStatus();
            ColorLog.Pink("火车状态", trainStatus);

            if (GameEntry.TradeTruckData.myTrain != null
            && (trainStatus == TrainStatus.LineUp || trainStatus == TrainStatus.Prepare))
            {
                m_btnBuy.gameObject.SetActive(false);
                m_btnEnter.gameObject.SetActive(true);
                m_btnExit.gameObject.SetActive(true);
            }
            else
            {
                ColorLog.Pink("初始化合约入口，火车状态", trainStatus);
                if (trainStatus == TrainStatus.None || trainStatus == TrainStatus.Depart || trainStatus == TrainStatus.Arrive)
                {
                    bool isJoin = GameEntry.LogicData.UnionData.IsJoinUnion();
                    ColorLog.Pink("初始化合约入口，是否加入联盟", isJoin);
                    m_btnBuy.gameObject.SetActive(isJoin);
                }
                else
                {
                    m_btnBuy.gameObject.SetActive(false);
                }
                m_btnEnter.gameObject.SetActive(false);
                m_btnExit.gameObject.SetActive(false);
            }

            m_txtCount.text = $"{GameEntry.TradeTruckData.BuyExpressContractTimes}/2";
        }

        protected override void OnUnInit()
        {
            base.OnUnInit();
            GameEntry.Event.Unsubscribe(TrainContractEventArgs.EventId, OnTrainContractEventArgs);
            GameEntry.Event.Unsubscribe(UnionJoinChangeEventArgs.EventId, OnUnionJoinChangeEventArgs);
        }

        private void OnTrainContractEventArgs(object sender, GameEventArgs e)
        {
            if (e is TrainContractEventArgs args)
            {
                m_btnBuy.gameObject.SetActive(args.IsShow);
                m_btnEnter.gameObject.SetActive(!args.IsShow);
                m_btnExit.gameObject.SetActive(!args.IsShow);
            }

            m_txtCount.text = $"{GameEntry.TradeTruckData.BuyExpressContractTimes}/2";
        }

        private void OnUnionJoinChangeEventArgs(object sender, GameEventArgs e)
        {
            TrainStatus trainStatus = GameEntry.TradeTruckData.GetTrainStatus();
            ColorLog.Pink("火车状态", trainStatus);
            if (trainStatus == TrainStatus.None || trainStatus == TrainStatus.Depart || trainStatus == TrainStatus.Arrive)
            {
                bool isJoin = GameEntry.LogicData.UnionData.IsJoinUnion();
                ColorLog.Pink("是否加入联盟", isJoin);
                m_btnBuy.gameObject.SetActive(isJoin);
            }
            else
            {
                m_btnBuy.gameObject.SetActive(false);
            }
        }

        protected override Vector3 GetOffset()
        {
            return new Vector3(8.2f, 3.2f, 1f);
        }

        void OnBtnBuyClick()
        {
            GameEntry.UI.OpenUIForm(EnumUIForm.UITradeTrainContractForm);
        }

        void OnBtnEnterClick()
        {
            GameEntry.Event.Fire(TrainAnimationEventArgs.EventId, TrainAnimationEventArgs.Create(TrainAnimationType.Enter));
        }

        void OnBtnExitClick()
        {
            GameEntry.Event.Fire(TrainAnimationEventArgs.EventId, TrainAnimationEventArgs.Create(TrainAnimationType.Exit));
        }
    }
}
